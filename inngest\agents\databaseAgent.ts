import { api } from "@/convex/_generated/api";
import { Id } from "@/convex/_generated/dataModel";
import convex from "@/lib/convexClient";
import { createAgent, createTool ,gemini, openai } from "@inngest/agent-kit";
import client from "@/lib/schematic";
import { z } from "zod";
import { prisma } from "@/lib/db";
import { Decimal } from "@prisma/client/runtime/library";

const saveToDatabaseTool = createTool({
  name: "save-to-database",
  description: "Saves the given data to the convex database",
  parameters: z.object({
    receiptId: z.string().describe("The ID of the receipt to update"),
    fileDisplayName: z.string().describe("The readable display name of the receipt to show in the UI. If the file name is not human readable, use this to give a more readable name."),
    merchantName: z.string(),
    merchantAddress: z.string(),
    merchantContact: z.string(),
    transactionDate: z.string(),
    transactionAmount: z.string().describe("The total amount of the transaction, summing all the items on the receipt"),
    receiptSummary: z.string().describe("A summary of the receipt, including the merchant name, address, contact, transaction date, transaction amount, and currency. Include a human readable summary of the receipt. Mention both invoice number and receipt number if both are present. Include some key details about the items on the receipt, this is a special featured summary, so it should include some key details about the items on the receipt with some context."),
    currency: z.string().describe("The currency of the receipt, this is a 3 letter currency code."),
    items: z.array(z.object({
      name: z.string(),
      quantity: z.number(),
      unitPrice: z.number(),
      totalPrice: z.number()
    })).describe("An array of items on the receipt. Include the name, quantity, unit price, and total price of each item.")
  }),
  handler: async (params, context) => {
    console.log("params", params)
    const { receiptId, fileDisplayName, merchantName, merchantAddress, merchantContact, transactionDate, transactionAmount, receiptSummary, currency, items } = params
    const result = await context.step?.run("save-receipt-to-database", 
      async () => {
        try {
          const { userId } = await convex.mutation(
            api.receipts.updateReceiptWithExtractedData,
            {
              id: receiptId as Id<"receipts">,
              fileDisplayName,
              merchantName,
              merchantAddress,
              merchantContact,
              transactionDate,
              transactionAmount,
              receiptSummary,
              currency,
              items
            }
          )

          // 將每個項目新增為單獨的支出記錄
          const expensePromises = items.map((item: {
            name: string;
            quantity: number;
            unitPrice: number;
            totalPrice: number;
          }) => 
            prisma.expense.create({
              data: {
                amount: new Decimal(item.totalPrice),
                date: new Date(transactionDate),
                description: `${item.name} (${merchantName}) - ${item.quantity}x @ ${item.unitPrice} ${currency}`,
                type: "DEBIT",
                userId,
                categoryValidated: false,
                categoryId: "cm4tropm0007dmmebndt20iyg",
              },
            })
          );

          await Promise.all(expensePromises);
          
          // Track event in schematic
          await client.track({
            event: "scan",
            company: {
              id: userId,
            },
            user: {
              id: userId,
            }
          })
          return {
            addedToDb: "Success",
            receiptId,
            fileDisplayName,
            merchantName,
            merchantAddress,
            merchantContact,
            transactionDate,
            transactionAmount,
            receiptSummary,
            currency,
            items
          }
        } catch (error) {
          return { 
            addedToDb: "Failed", 
            error: error instanceof Error ? error.message : "Unknown error" 
          }
        }
    })

    if (result?.addedToDb === "Success") {
      // Only set KV values if the operation was successful
      if (context.network?.state.data) {
        //context.network?.state.kv.set("saved-to-database", true)
        //context.network?.state.kv.set("receipt", receiptId)
        context.network.state.data["saved-to-database"] = true;
        context.network.state.data["receipt"] = receiptId;
      }
    }

    return result
  }
})

const openAIModel = openai({
  model: "gpt-4o-mini",
  defaultParameters: {
    max_completion_tokens: 1000,
  },
})

const geminiModel = gemini({
  model: "gemini-2.5-pro-exp-03-25",
  baseUrl: "https://generativelanguage.googleapis.com/v1beta/",
  defaultParameters: {
    generationConfig: {
      maxOutputTokens: 1000,
    },
  },
})

export const databaseAgent = createAgent({
  name: "Database Agent",
  description: "responsible for taking key information regarding receipts and saving it to the convex database",
  system: `You are a helpful assistant that takes key information regarding receipts and saves it to the convex database.`,
  model: openAIModel,
  tools: [saveToDatabaseTool],
})
