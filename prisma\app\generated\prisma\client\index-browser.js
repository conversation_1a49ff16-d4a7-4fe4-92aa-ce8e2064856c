
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('./runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.15.0
 * Query Engine version: 85179d7826409ee107a6ba334b5e305ae3fba9fb
 */
Prisma.prismaVersion = {
  client: "6.15.0",
  engine: "85179d7826409ee107a6ba334b5e305ae3fba9fb"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  name: 'name',
  email: 'email',
  phone: 'phone',
  plan: 'plan',
  credits: 'credits',
  image: 'image',
  language: 'language',
  timezone: 'timezone',
  onboardingEmailSent: 'onboardingEmailSent',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  stripeCustomerId: 'stripeCustomerId',
  stripeSubscriptionId: 'stripeSubscriptionId',
  stripePriceId: 'stripePriceId',
  stripeCurrentPeriodEnd: 'stripeCurrentPeriodEnd'
};

exports.Prisma.WorkspaceScalarFieldEnum = {
  id: 'id',
  name: 'name',
  orgnr: 'orgnr',
  address: 'address',
  postalCode: 'postalCode',
  city: 'city',
  inviteCode: 'inviteCode',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.MemberScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  bankAccountId: 'bankAccountId',
  workspaceId: 'workspaceId',
  role: 'role'
};

exports.Prisma.ProjectScalarFieldEnum = {
  id: 'id',
  name: 'name',
  userId: 'userId',
  workspaceId: 'workspaceId',
  imageUrl: 'imageUrl',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TaskScalarFieldEnum = {
  id: 'id',
  name: 'name',
  userId: 'userId',
  workspaceId: 'workspaceId',
  projectId: 'projectId',
  assigneeId: 'assigneeId',
  description: 'description',
  startDate: 'startDate',
  dueDate: 'dueDate',
  status: 'status',
  position: 'position',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ReminderScalarFieldEnum = {
  id: 'id',
  taskId: 'taskId',
  enabled: 'enabled',
  basis: 'basis',
  daysBefore: 'daysBefore',
  customDate: 'customDate',
  workflowRunId: 'workflowRunId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.WorkflowRunScalarFieldEnum = {
  id: 'id',
  reminderId: 'reminderId',
  workflowRunId: 'workflowRunId',
  status: 'status',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.BankAccountScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  resourceId: 'resourceId',
  originalId: 'originalId',
  orgId: 'orgId',
  userId: 'userId',
  name: 'name',
  originalPayload: 'originalPayload',
  initialAmount: 'initialAmount',
  accountType: 'accountType'
};

exports.Prisma.BalanceScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  accountId: 'accountId',
  assetId: 'assetId',
  currencyIso: 'currencyIso',
  amount: 'amount',
  date: 'date',
  description: 'description',
  type: 'type',
  originalPayload: 'originalPayload'
};

exports.Prisma.TimeDepositScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  accountId: 'accountId',
  assetId: 'assetId',
  categoryId: 'categoryId',
  currencyIso: 'currencyIso',
  amount: 'amount',
  date: 'date',
  certificateNo: 'certificateNo',
  period: 'period',
  interestRate: 'interestRate',
  description: 'description',
  type: 'type',
  originalPayload: 'originalPayload',
  categoryValidated: 'categoryValidated',
  suggestedCategoryId: 'suggestedCategoryId'
};

exports.Prisma.CurrencyScalarFieldEnum = {
  iso: 'iso',
  symbol: 'symbol',
  name: 'name',
  numericCode: 'numericCode'
};

exports.Prisma.AssetScalarFieldEnum = {
  id: 'id',
  name: 'name',
  originalPayload: 'originalPayload',
  type: 'type',
  value: 'value',
  purchaseDate: 'purchaseDate',
  description: 'description',
  userId: 'userId'
};

exports.Prisma.AssetValuationScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  value: 'value',
  date: 'date',
  assetId: 'assetId'
};

exports.Prisma.AssetTransactionScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  type: 'type',
  amount: 'amount',
  date: 'date',
  description: 'description',
  assetId: 'assetId'
};

exports.Prisma.TransactionScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  accountId: 'accountId',
  assetId: 'assetId',
  userId: 'userId',
  currencyIso: 'currencyIso',
  categoryId: 'categoryId',
  amount: 'amount',
  date: 'date',
  description: 'description',
  originalPayload: 'originalPayload',
  review: 'review',
  type: 'type',
  categoryValidated: 'categoryValidated',
  suggestedCategoryId: 'suggestedCategoryId'
};

exports.Prisma.IncomeScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  amount: 'amount',
  description: 'description',
  date: 'date',
  categoryId: 'categoryId',
  userId: 'userId',
  accountId: 'accountId',
  type: 'type',
  categoryValidated: 'categoryValidated',
  suggestedCategoryId: 'suggestedCategoryId',
  importHash: 'importHash'
};

exports.Prisma.ExpenseScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  amount: 'amount',
  description: 'description',
  date: 'date',
  categoryId: 'categoryId',
  userId: 'userId',
  accountId: 'accountId',
  type: 'type',
  categoryValidated: 'categoryValidated',
  suggestedCategoryId: 'suggestedCategoryId',
  importHash: 'importHash'
};

exports.Prisma.SpreadsheetImportScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  importedAt: 'importedAt',
  fileName: 'fileName',
  sheetName: 'sheetName',
  rowCount: 'rowCount',
  type: 'type',
  importHash: 'importHash'
};

exports.Prisma.CategoryScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  name: 'name',
  icon: 'icon',
  color: 'color',
  keywords: 'keywords',
  type: 'type',
  userId: 'userId',
  description: 'description'
};

exports.Prisma.BudgetPreferencesScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  autoCreateNextBudget: 'autoCreateNextBudget',
  autoApplyRollovers: 'autoApplyRollovers',
  rolloverThreshold: 'rolloverThreshold'
};

exports.Prisma.BudgetScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  name: 'name',
  startDate: 'startDate',
  endDate: 'endDate',
  amount: 'amount',
  userId: 'userId',
  accountId: 'accountId'
};

exports.Prisma.CategoryBudgetScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  amount: 'amount',
  budgetId: 'budgetId',
  categoryId: 'categoryId'
};

exports.Prisma.BudgetRolloverScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  categoryId: 'categoryId',
  previousBudgetId: 'previousBudgetId',
  nextBudgetId: 'nextBudgetId',
  amount: 'amount',
  createdAt: 'createdAt',
  periodStart: 'periodStart',
  periodEnd: 'periodEnd',
  rolloverPercentage: 'rolloverPercentage',
  accountId: 'accountId'
};

exports.Prisma.ConnectorConfigScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  orgId: 'orgId',
  secret: 'secret',
  env: 'env',
  connectorId: 'connectorId'
};

exports.Prisma.ConnectorScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  name: 'name',
  logoUrl: 'logoUrl',
  status: 'status',
  type: 'type'
};

exports.Prisma.IntegrationScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  name: 'name',
  logoUrl: 'logoUrl',
  connectorProviderId: 'connectorProviderId',
  connectorId: 'connectorId'
};

exports.Prisma.ResourceScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  integrationId: 'integrationId',
  originalId: 'originalId',
  userId: 'userId'
};

exports.Prisma.StatsScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  totalUsers: 'totalUsers',
  activeUsers: 'activeUsers',
  totalAccounts: 'totalAccounts',
  totalTransactions: 'totalTransactions',
  totalAssets: 'totalAssets',
  totalLiabilities: 'totalLiabilities',
  totalInvestments: 'totalInvestments',
  avgAccountsPerUser: 'avgAccountsPerUser',
  avgTransactionsPerUser: 'avgTransactionsPerUser',
  dailyActiveUsers: 'dailyActiveUsers',
  weeklyActiveUsers: 'weeklyActiveUsers',
  monthlyActiveUsers: 'monthlyActiveUsers',
  operatingSystem: 'operatingSystem',
  browser: 'browser',
  country: 'country',
  lastUpdated: 'lastUpdated'
};

exports.Prisma.StatsHistoryScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  totalUsers: 'totalUsers',
  activeUsers: 'activeUsers',
  totalAccounts: 'totalAccounts',
  totalTransactions: 'totalTransactions',
  totalAssets: 'totalAssets',
  totalLiabilities: 'totalLiabilities',
  totalInvestments: 'totalInvestments',
  dailyActiveUsers: 'dailyActiveUsers',
  weeklyActiveUsers: 'weeklyActiveUsers',
  monthlyActiveUsers: 'monthlyActiveUsers',
  snapshot: 'snapshot'
};

exports.Prisma.InvestmentScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  name: 'name',
  type: 'type',
  amount: 'amount',
  shares: 'shares',
  purchasePrice: 'purchasePrice',
  currentPrice: 'currentPrice',
  description: 'description',
  userId: 'userId'
};

exports.Prisma.InvestmentValuationScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  value: 'value',
  date: 'date',
  investmentId: 'investmentId'
};

exports.Prisma.InvestmentTransactionScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  type: 'type',
  amount: 'amount',
  shares: 'shares',
  price: 'price',
  date: 'date',
  description: 'description',
  investmentId: 'investmentId'
};

exports.Prisma.LiabilityScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  name: 'name',
  type: 'type',
  amount: 'amount',
  interestRate: 'interestRate',
  monthlyPayment: 'monthlyPayment',
  startDate: 'startDate',
  endDate: 'endDate',
  description: 'description',
  userId: 'userId'
};

exports.Prisma.LiabilityPaymentScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  amount: 'amount',
  date: 'date',
  type: 'type',
  description: 'description',
  liabilityId: 'liabilityId'
};

exports.Prisma.SavingsGoalScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  name: 'name',
  target: 'target',
  current: 'current',
  deadline: 'deadline',
  description: 'description',
  isDefault: 'isDefault',
  type: 'type',
  priority: 'priority',
  userId: 'userId'
};

exports.Prisma.SavingsProgressScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  amount: 'amount',
  date: 'date',
  goalId: 'goalId'
};

exports.Prisma.ContractAnalysisScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  contractText: 'contractText',
  summary: 'summary',
  recommendations: 'recommendations',
  keyClauses: 'keyClauses',
  legalCompliance: 'legalCompliance',
  negotiationPoints: 'negotiationPoints',
  contractDuration: 'contractDuration',
  terminationConditions: 'terminationConditions',
  overallScore: 'overallScore',
  performanceMetrics: 'performanceMetrics',
  intellectualPropertyClauses: 'intellectualPropertyClauses',
  createdAt: 'createdAt',
  version: 'version',
  userFeedback: 'userFeedback',
  customFields: 'customFields',
  expirationDate: 'expirationDate',
  language: 'language',
  filePath: 'filePath',
  contractType: 'contractType',
  financialTerms: 'financialTerms',
  specificClauses: 'specificClauses'
};

exports.Prisma.RiskScalarFieldEnum = {
  id: 'id',
  contractId: 'contractId',
  risk: 'risk',
  explanation: 'explanation',
  severity: 'severity'
};

exports.Prisma.OpportunityScalarFieldEnum = {
  id: 'id',
  contractId: 'contractId',
  opportunity: 'opportunity',
  explanation: 'explanation',
  impact: 'impact'
};

exports.Prisma.CompensationStructureScalarFieldEnum = {
  id: 'id',
  contractId: 'contractId',
  baseSalary: 'baseSalary',
  bonuses: 'bonuses',
  equity: 'equity',
  otherBenefits: 'otherBenefits'
};

exports.Prisma.SpeechScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  workspaceId: 'workspaceId',
  projectId: 'projectId',
  title: 'title',
  description: 'description',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.RecordingScalarFieldEnum = {
  id: 'id',
  speechId: 'speechId',
  url: 'url',
  duration: 'duration',
  createdAt: 'createdAt'
};

exports.Prisma.TranscriptionScalarFieldEnum = {
  id: 'id',
  speechId: 'speechId',
  text: 'text',
  language: 'language',
  createdAt: 'createdAt'
};

exports.Prisma.AnalysisScalarFieldEnum = {
  id: 'id',
  speechId: 'speechId',
  summary: 'summary',
  keyPoints: 'keyPoints',
  sentiment: 'sentiment',
  createdAt: 'createdAt'
};

exports.Prisma.VectorStoreScalarFieldEnum = {
  id: 'id',
  documentId: 'documentId',
  documentType: 'documentType',
  content: 'content',
  metadata: 'metadata',
  filePath: 'filePath',
  createdAt: 'createdAt'
};

exports.Prisma.AssistantScalarFieldEnum = {
  assistant_id: 'assistant_id',
  graph_id: 'graph_id',
  config: 'config',
  created_at: 'created_at',
  updated_at: 'updated_at',
  metadata: 'metadata',
  version: 'version',
  name: 'name',
  description: 'description'
};

exports.Prisma.ThreadScalarFieldEnum = {
  thread_id: 'thread_id',
  created_at: 'created_at',
  updated_at: 'updated_at',
  metadata: 'metadata',
  status: 'status',
  values: 'values'
};

exports.Prisma.RunScalarFieldEnum = {
  run_id: 'run_id',
  thread_id: 'thread_id',
  assistant_id: 'assistant_id',
  created_at: 'created_at',
  updated_at: 'updated_at',
  status: 'status',
  metadata: 'metadata',
  multitask_strategy: 'multitask_strategy'
};

exports.Prisma.CheckpointScalarFieldEnum = {
  thread_id: 'thread_id',
  checkpoint_ns: 'checkpoint_ns',
  checkpoint_id: 'checkpoint_id',
  parent_checkpoint_id: 'parent_checkpoint_id',
  type: 'type',
  checkpoint: 'checkpoint',
  metadata: 'metadata'
};

exports.Prisma.CheckpointBlobsScalarFieldEnum = {
  thread_id: 'thread_id',
  checkpoint_ns: 'checkpoint_ns',
  channel: 'channel',
  version: 'version',
  type: 'type',
  blob: 'blob'
};

exports.Prisma.CheckpointWritesScalarFieldEnum = {
  thread_id: 'thread_id',
  checkpoint_ns: 'checkpoint_ns',
  checkpoint_id: 'checkpoint_id',
  task_id: 'task_id',
  idx: 'idx',
  channel: 'channel',
  type: 'type',
  blob: 'blob'
};

exports.Prisma.CheckpointMigrationsScalarFieldEnum = {
  v: 'v'
};

exports.Prisma.ChatScalarFieldEnum = {
  id: 'id',
  assistantId: 'assistantId',
  contractId: 'contractId',
  createdAt: 'createdAt',
  title: 'title',
  userId: 'userId',
  visibility: 'visibility'
};

exports.Prisma.MessageScalarFieldEnum = {
  id: 'id',
  chatId: 'chatId',
  role: 'role',
  content: 'content',
  createdAt: 'createdAt'
};

exports.Prisma.MemoryStoreScalarFieldEnum = {
  id: 'id',
  namespace: 'namespace',
  key: 'key',
  value: 'value',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.BannerScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  title: 'title',
  type: 'type',
  size: 'size',
  fontWeight: 'fontWeight',
  color: 'color',
  description: 'description',
  shared: 'shared',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.InventoryScalarFieldEnum = {
  id: 'id',
  itemType: 'itemType',
  current: 'current',
  price: 'price',
  minStock: 'minStock',
  maxStock: 'maxStock',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ResidentScalarFieldEnum = {
  id: 'id',
  name: 'name',
  address: 'address',
  phone: 'phone',
  email: 'email',
  building: 'building',
  floor: 'floor',
  unit: 'unit',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SaleScalarFieldEnum = {
  id: 'id',
  date: 'date',
  receiptNo: 'receiptNo',
  recipient: 'recipient',
  notes: 'notes',
  totalAmount: 'totalAmount',
  residentId: 'residentId',
  residentName: 'residentName',
  address: 'address',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SaleItemScalarFieldEnum = {
  id: 'id',
  saleId: 'saleId',
  inventoryId: 'inventoryId',
  itemType: 'itemType',
  quantity: 'quantity',
  unitPrice: 'unitPrice',
  lineTotal: 'lineTotal',
  createdAt: 'createdAt'
};

exports.Prisma.StockMovementScalarFieldEnum = {
  id: 'id',
  inventoryId: 'inventoryId',
  type: 'type',
  quantity: 'quantity',
  reason: 'reason',
  reference: 'reference',
  previousStock: 'previousStock',
  newStock: 'newStock',
  createdAt: 'createdAt',
  createdBy: 'createdBy'
};

exports.Prisma.PaymentRequestScalarFieldEnum = {
  id: 'id',
  name: 'name',
  year: 'year',
  month: 'month',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PaymentRequestAccountScalarFieldEnum = {
  id: 'id',
  paymentRequestId: 'paymentRequestId',
  paymentAccountId: 'paymentAccountId',
  isPreferred: 'isPreferred',
  createdAt: 'createdAt'
};

exports.Prisma.PaymentAccountScalarFieldEnum = {
  id: 'id',
  accountType: 'accountType',
  bankCode: 'bankCode',
  bankName: 'bankName',
  branchName: 'branchName',
  accountNumber: 'accountNumber',
  accountName: 'accountName',
  isDefault: 'isDefault',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PaymentScalarFieldEnum = {
  id: 'id',
  sequenceId: 'sequenceId',
  accountingSubject: 'accountingSubject',
  payee: 'payee',
  remarks: 'remarks',
  month: 'month',
  amount: 'amount',
  paymentMethod: 'paymentMethod',
  showAccountInfo: 'showAccountInfo',
  remitterAccountId: 'remitterAccountId',
  payeeAccountId: 'payeeAccountId',
  paymentRequestId: 'paymentRequestId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.MeetingReportScalarFieldEnum = {
  id: 'id',
  title: 'title',
  slug: 'slug',
  content: 'content',
  reportType: 'reportType',
  reportDate: 'reportDate',
  published: 'published',
  workspaceId: 'workspaceId',
  visibility: 'visibility',
  userId: 'userId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.JsonNullValueInput = {
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.UserOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  email: 'email',
  phone: 'phone',
  plan: 'plan',
  image: 'image',
  language: 'language',
  timezone: 'timezone',
  stripeCustomerId: 'stripeCustomerId',
  stripeSubscriptionId: 'stripeSubscriptionId',
  stripePriceId: 'stripePriceId'
};

exports.Prisma.WorkspaceOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  address: 'address',
  postalCode: 'postalCode',
  city: 'city',
  inviteCode: 'inviteCode'
};

exports.Prisma.MemberOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId',
  bankAccountId: 'bankAccountId',
  workspaceId: 'workspaceId'
};

exports.Prisma.ProjectOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  userId: 'userId',
  workspaceId: 'workspaceId',
  imageUrl: 'imageUrl'
};

exports.Prisma.TaskOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  userId: 'userId',
  workspaceId: 'workspaceId',
  projectId: 'projectId',
  assigneeId: 'assigneeId',
  description: 'description'
};

exports.Prisma.ReminderOrderByRelevanceFieldEnum = {
  id: 'id',
  taskId: 'taskId',
  workflowRunId: 'workflowRunId'
};

exports.Prisma.WorkflowRunOrderByRelevanceFieldEnum = {
  id: 'id',
  reminderId: 'reminderId',
  workflowRunId: 'workflowRunId',
  status: 'status'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};

exports.Prisma.BankAccountOrderByRelevanceFieldEnum = {
  id: 'id',
  resourceId: 'resourceId',
  originalId: 'originalId',
  orgId: 'orgId',
  userId: 'userId',
  name: 'name'
};

exports.Prisma.BalanceOrderByRelevanceFieldEnum = {
  id: 'id',
  accountId: 'accountId',
  assetId: 'assetId',
  currencyIso: 'currencyIso',
  description: 'description'
};

exports.Prisma.TimeDepositOrderByRelevanceFieldEnum = {
  id: 'id',
  accountId: 'accountId',
  assetId: 'assetId',
  categoryId: 'categoryId',
  currencyIso: 'currencyIso',
  certificateNo: 'certificateNo',
  period: 'period',
  description: 'description',
  suggestedCategoryId: 'suggestedCategoryId'
};

exports.Prisma.CurrencyOrderByRelevanceFieldEnum = {
  iso: 'iso',
  symbol: 'symbol',
  name: 'name'
};

exports.Prisma.AssetOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  userId: 'userId'
};

exports.Prisma.AssetValuationOrderByRelevanceFieldEnum = {
  id: 'id',
  assetId: 'assetId'
};

exports.Prisma.AssetTransactionOrderByRelevanceFieldEnum = {
  id: 'id',
  description: 'description',
  assetId: 'assetId'
};

exports.Prisma.TransactionOrderByRelevanceFieldEnum = {
  id: 'id',
  accountId: 'accountId',
  assetId: 'assetId',
  userId: 'userId',
  currencyIso: 'currencyIso',
  categoryId: 'categoryId',
  description: 'description',
  type: 'type',
  suggestedCategoryId: 'suggestedCategoryId'
};

exports.Prisma.IncomeOrderByRelevanceFieldEnum = {
  id: 'id',
  description: 'description',
  categoryId: 'categoryId',
  userId: 'userId',
  accountId: 'accountId',
  type: 'type',
  suggestedCategoryId: 'suggestedCategoryId',
  importHash: 'importHash'
};

exports.Prisma.ExpenseOrderByRelevanceFieldEnum = {
  id: 'id',
  description: 'description',
  categoryId: 'categoryId',
  userId: 'userId',
  accountId: 'accountId',
  type: 'type',
  suggestedCategoryId: 'suggestedCategoryId',
  importHash: 'importHash'
};

exports.Prisma.SpreadsheetImportOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId',
  fileName: 'fileName',
  sheetName: 'sheetName',
  type: 'type',
  importHash: 'importHash'
};

exports.Prisma.CategoryOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  icon: 'icon',
  color: 'color',
  keywords: 'keywords',
  userId: 'userId',
  description: 'description'
};

exports.Prisma.BudgetPreferencesOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId'
};

exports.Prisma.BudgetOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  userId: 'userId',
  accountId: 'accountId'
};

exports.Prisma.CategoryBudgetOrderByRelevanceFieldEnum = {
  id: 'id',
  budgetId: 'budgetId',
  categoryId: 'categoryId'
};

exports.Prisma.BudgetRolloverOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId',
  categoryId: 'categoryId',
  previousBudgetId: 'previousBudgetId',
  nextBudgetId: 'nextBudgetId',
  accountId: 'accountId'
};

exports.Prisma.ConnectorConfigOrderByRelevanceFieldEnum = {
  id: 'id',
  orgId: 'orgId',
  connectorId: 'connectorId'
};

exports.Prisma.ConnectorOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  logoUrl: 'logoUrl'
};

exports.Prisma.IntegrationOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  logoUrl: 'logoUrl',
  connectorProviderId: 'connectorProviderId',
  connectorId: 'connectorId'
};

exports.Prisma.ResourceOrderByRelevanceFieldEnum = {
  id: 'id',
  integrationId: 'integrationId',
  originalId: 'originalId',
  userId: 'userId'
};

exports.Prisma.StatsOrderByRelevanceFieldEnum = {
  id: 'id'
};

exports.Prisma.StatsHistoryOrderByRelevanceFieldEnum = {
  id: 'id'
};

exports.Prisma.InvestmentOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  userId: 'userId'
};

exports.Prisma.InvestmentValuationOrderByRelevanceFieldEnum = {
  id: 'id',
  investmentId: 'investmentId'
};

exports.Prisma.InvestmentTransactionOrderByRelevanceFieldEnum = {
  id: 'id',
  description: 'description',
  investmentId: 'investmentId'
};

exports.Prisma.LiabilityOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  userId: 'userId'
};

exports.Prisma.LiabilityPaymentOrderByRelevanceFieldEnum = {
  id: 'id',
  description: 'description',
  liabilityId: 'liabilityId'
};

exports.Prisma.SavingsGoalOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  userId: 'userId'
};

exports.Prisma.SavingsProgressOrderByRelevanceFieldEnum = {
  id: 'id',
  goalId: 'goalId'
};

exports.Prisma.ContractAnalysisOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId',
  contractText: 'contractText',
  summary: 'summary',
  recommendations: 'recommendations',
  keyClauses: 'keyClauses',
  legalCompliance: 'legalCompliance',
  negotiationPoints: 'negotiationPoints',
  contractDuration: 'contractDuration',
  terminationConditions: 'terminationConditions',
  performanceMetrics: 'performanceMetrics',
  language: 'language',
  filePath: 'filePath',
  contractType: 'contractType',
  specificClauses: 'specificClauses'
};

exports.Prisma.RiskOrderByRelevanceFieldEnum = {
  id: 'id',
  contractId: 'contractId',
  risk: 'risk',
  explanation: 'explanation',
  severity: 'severity'
};

exports.Prisma.OpportunityOrderByRelevanceFieldEnum = {
  id: 'id',
  contractId: 'contractId',
  opportunity: 'opportunity',
  explanation: 'explanation',
  impact: 'impact'
};

exports.Prisma.CompensationStructureOrderByRelevanceFieldEnum = {
  id: 'id',
  contractId: 'contractId',
  baseSalary: 'baseSalary',
  bonuses: 'bonuses',
  equity: 'equity',
  otherBenefits: 'otherBenefits'
};

exports.Prisma.SpeechOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId',
  workspaceId: 'workspaceId',
  projectId: 'projectId',
  title: 'title',
  description: 'description'
};

exports.Prisma.RecordingOrderByRelevanceFieldEnum = {
  id: 'id',
  speechId: 'speechId',
  url: 'url'
};

exports.Prisma.TranscriptionOrderByRelevanceFieldEnum = {
  id: 'id',
  speechId: 'speechId',
  text: 'text',
  language: 'language'
};

exports.Prisma.AnalysisOrderByRelevanceFieldEnum = {
  id: 'id',
  speechId: 'speechId',
  summary: 'summary',
  sentiment: 'sentiment'
};

exports.Prisma.VectorStoreOrderByRelevanceFieldEnum = {
  id: 'id',
  documentId: 'documentId',
  documentType: 'documentType',
  content: 'content',
  filePath: 'filePath'
};

exports.Prisma.AssistantOrderByRelevanceFieldEnum = {
  assistant_id: 'assistant_id',
  graph_id: 'graph_id',
  name: 'name',
  description: 'description'
};

exports.Prisma.ThreadOrderByRelevanceFieldEnum = {
  thread_id: 'thread_id'
};

exports.Prisma.RunOrderByRelevanceFieldEnum = {
  run_id: 'run_id',
  thread_id: 'thread_id',
  assistant_id: 'assistant_id'
};

exports.Prisma.CheckpointOrderByRelevanceFieldEnum = {
  thread_id: 'thread_id',
  checkpoint_ns: 'checkpoint_ns',
  checkpoint_id: 'checkpoint_id',
  parent_checkpoint_id: 'parent_checkpoint_id',
  type: 'type'
};

exports.Prisma.CheckpointBlobsOrderByRelevanceFieldEnum = {
  thread_id: 'thread_id',
  checkpoint_ns: 'checkpoint_ns',
  channel: 'channel',
  version: 'version',
  type: 'type'
};

exports.Prisma.CheckpointWritesOrderByRelevanceFieldEnum = {
  thread_id: 'thread_id',
  checkpoint_ns: 'checkpoint_ns',
  checkpoint_id: 'checkpoint_id',
  task_id: 'task_id',
  channel: 'channel',
  type: 'type'
};

exports.Prisma.ChatOrderByRelevanceFieldEnum = {
  id: 'id',
  assistantId: 'assistantId',
  contractId: 'contractId',
  title: 'title',
  userId: 'userId'
};

exports.Prisma.MessageOrderByRelevanceFieldEnum = {
  id: 'id',
  chatId: 'chatId',
  role: 'role'
};

exports.Prisma.MemoryStoreOrderByRelevanceFieldEnum = {
  id: 'id',
  namespace: 'namespace',
  key: 'key'
};

exports.Prisma.BannerOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId',
  title: 'title',
  type: 'type',
  size: 'size',
  fontWeight: 'fontWeight',
  color: 'color',
  description: 'description'
};

exports.Prisma.InventoryOrderByRelevanceFieldEnum = {
  id: 'id'
};

exports.Prisma.ResidentOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  address: 'address',
  phone: 'phone',
  email: 'email',
  building: 'building',
  floor: 'floor',
  unit: 'unit'
};

exports.Prisma.SaleOrderByRelevanceFieldEnum = {
  id: 'id',
  receiptNo: 'receiptNo',
  recipient: 'recipient',
  notes: 'notes',
  residentId: 'residentId',
  residentName: 'residentName',
  address: 'address'
};

exports.Prisma.SaleItemOrderByRelevanceFieldEnum = {
  id: 'id',
  saleId: 'saleId',
  inventoryId: 'inventoryId'
};

exports.Prisma.StockMovementOrderByRelevanceFieldEnum = {
  id: 'id',
  inventoryId: 'inventoryId',
  reason: 'reason',
  reference: 'reference',
  createdBy: 'createdBy'
};

exports.Prisma.PaymentRequestOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  year: 'year',
  month: 'month'
};

exports.Prisma.PaymentRequestAccountOrderByRelevanceFieldEnum = {
  id: 'id',
  paymentRequestId: 'paymentRequestId',
  paymentAccountId: 'paymentAccountId'
};

exports.Prisma.PaymentAccountOrderByRelevanceFieldEnum = {
  id: 'id',
  bankCode: 'bankCode',
  bankName: 'bankName',
  branchName: 'branchName',
  accountNumber: 'accountNumber',
  accountName: 'accountName'
};

exports.Prisma.PaymentOrderByRelevanceFieldEnum = {
  id: 'id',
  sequenceId: 'sequenceId',
  accountingSubject: 'accountingSubject',
  payee: 'payee',
  remarks: 'remarks',
  month: 'month',
  paymentMethod: 'paymentMethod',
  remitterAccountId: 'remitterAccountId',
  payeeAccountId: 'payeeAccountId',
  paymentRequestId: 'paymentRequestId'
};

exports.Prisma.MeetingReportOrderByRelevanceFieldEnum = {
  id: 'id',
  title: 'title',
  slug: 'slug',
  content: 'content',
  workspaceId: 'workspaceId',
  userId: 'userId'
};
exports.Role = exports.$Enums.Role = {
  ADMIN: 'ADMIN',
  MEMBER: 'MEMBER'
};

exports.TaskStatus = exports.$Enums.TaskStatus = {
  BACKLOG: 'BACKLOG',
  TODO: 'TODO',
  IN_PROGRESS: 'IN_PROGRESS',
  IN_REVIEW: 'IN_REVIEW',
  DONE: 'DONE'
};

exports.ReminderBasis = exports.$Enums.ReminderBasis = {
  START_DATE: 'START_DATE',
  DUE_DATE: 'DUE_DATE'
};

exports.AccountType = exports.$Enums.AccountType = {
  BANK: 'BANK',
  SAVINGS: 'SAVINGS',
  INVESTMENT: 'INVESTMENT',
  CRYPTO: 'CRYPTO',
  VIRTUAL: 'VIRTUAL'
};

exports.BalanceType = exports.$Enums.BalanceType = {
  AVAILABLE: 'AVAILABLE',
  BOOKED: 'BOOKED',
  EXPECTED: 'EXPECTED'
};

exports.TimeDepositType = exports.$Enums.TimeDepositType = {
  AVAILABLE: 'AVAILABLE',
  WITHDRAWN: 'WITHDRAWN'
};

exports.AssetType = exports.$Enums.AssetType = {
  REAL_ESTATE: 'REAL_ESTATE',
  VEHICLE: 'VEHICLE',
  PRECIOUS_METALS: 'PRECIOUS_METALS',
  OTHER: 'OTHER'
};

exports.TransactionType = exports.$Enums.TransactionType = {
  PURCHASE: 'PURCHASE',
  SALE: 'SALE',
  MAINTENANCE: 'MAINTENANCE',
  IMPROVEMENT: 'IMPROVEMENT',
  DEPRECIATION: 'DEPRECIATION',
  APPRECIATION: 'APPRECIATION'
};

exports.CategoryType = exports.$Enums.CategoryType = {
  CREDIT: 'CREDIT',
  DEBIT: 'DEBIT'
};

exports.ConnectorEnv = exports.$Enums.ConnectorEnv = {
  DEVELOPMENT: 'DEVELOPMENT',
  SANDBOX: 'SANDBOX',
  PRODUCTION: 'PRODUCTION'
};

exports.ConnectorStatus = exports.$Enums.ConnectorStatus = {
  ACTIVE: 'ACTIVE',
  BETA: 'BETA',
  DEV: 'DEV',
  INACTIVE: 'INACTIVE'
};

exports.ConnectorType = exports.$Enums.ConnectorType = {
  DIRECT: 'DIRECT',
  AGGREGATED: 'AGGREGATED'
};

exports.InvestmentType = exports.$Enums.InvestmentType = {
  STOCKS: 'STOCKS',
  CRYPTO: 'CRYPTO',
  ETF: 'ETF',
  OTHER: 'OTHER'
};

exports.InvestmentTransactionType = exports.$Enums.InvestmentTransactionType = {
  BUY: 'BUY',
  SELL: 'SELL',
  DIVIDEND: 'DIVIDEND',
  SPLIT: 'SPLIT',
  MERGE: 'MERGE'
};

exports.LiabilityType = exports.$Enums.LiabilityType = {
  MORTGAGE: 'MORTGAGE',
  CREDIT_CARD: 'CREDIT_CARD',
  CAR_LOAN: 'CAR_LOAN',
  STUDENT_LOAN: 'STUDENT_LOAN'
};

exports.PaymentType = exports.$Enums.PaymentType = {
  REGULAR: 'REGULAR',
  EXTRA: 'EXTRA',
  INTEREST: 'INTEREST',
  PRINCIPAL: 'PRINCIPAL'
};

exports.GoalType = exports.$Enums.GoalType = {
  EMERGENCY_FUND: 'EMERGENCY_FUND',
  RETIREMENT: 'RETIREMENT',
  DOWN_PAYMENT: 'DOWN_PAYMENT',
  CUSTOM: 'CUSTOM'
};

exports.ThreadStatus = exports.$Enums.ThreadStatus = {
  idle: 'idle',
  busy: 'busy',
  interrupted: 'interrupted'
};

exports.RunStatus = exports.$Enums.RunStatus = {
  pending: 'pending',
  running: 'running',
  error: 'error',
  success: 'success',
  timeout: 'timeout',
  interrupted: 'interrupted'
};

exports.MultitaskStrategy = exports.$Enums.MultitaskStrategy = {
  reject: 'reject',
  interrupt: 'interrupt',
  rollback: 'rollback',
  enqueue: 'enqueue'
};

exports.Visibility = exports.$Enums.Visibility = {
  PUBLIC: 'PUBLIC',
  PRIVATE: 'PRIVATE'
};

exports.ItemType = exports.$Enums.ItemType = {
  A_BUILDING_CARD: 'A_BUILDING_CARD',
  B_BUILDING_CARD: 'B_BUILDING_CARD',
  ALL_AREA_CARD: 'ALL_AREA_CARD',
  MAIN_GATE_REMOTE_CONTROL: 'MAIN_GATE_REMOTE_CONTROL'
};

exports.StockMovementType = exports.$Enums.StockMovementType = {
  SALE: 'SALE',
  PURCHASE: 'PURCHASE',
  ADJUSTMENT: 'ADJUSTMENT',
  RETURN: 'RETURN',
  DAMAGE: 'DAMAGE',
  INITIAL: 'INITIAL'
};

exports.PaymentAccountType = exports.$Enums.PaymentAccountType = {
  REMITTER: 'REMITTER',
  PAYEE: 'PAYEE',
  BOTH: 'BOTH'
};

exports.ReportType = exports.$Enums.ReportType = {
  MONTHLY: 'MONTHLY',
  ANNUAL: 'ANNUAL'
};

exports.ContentVisibility = exports.$Enums.ContentVisibility = {
  PUBLIC: 'PUBLIC',
  WORKSPACE_MEMBERS: 'WORKSPACE_MEMBERS'
};

exports.Prisma.ModelName = {
  User: 'User',
  Workspace: 'Workspace',
  Member: 'Member',
  Project: 'Project',
  Task: 'Task',
  Reminder: 'Reminder',
  WorkflowRun: 'WorkflowRun',
  BankAccount: 'BankAccount',
  Balance: 'Balance',
  TimeDeposit: 'TimeDeposit',
  Currency: 'Currency',
  Asset: 'Asset',
  AssetValuation: 'AssetValuation',
  AssetTransaction: 'AssetTransaction',
  Transaction: 'Transaction',
  Income: 'Income',
  Expense: 'Expense',
  SpreadsheetImport: 'SpreadsheetImport',
  Category: 'Category',
  BudgetPreferences: 'BudgetPreferences',
  Budget: 'Budget',
  CategoryBudget: 'CategoryBudget',
  BudgetRollover: 'BudgetRollover',
  ConnectorConfig: 'ConnectorConfig',
  Connector: 'Connector',
  Integration: 'Integration',
  Resource: 'Resource',
  Stats: 'Stats',
  StatsHistory: 'StatsHistory',
  Investment: 'Investment',
  InvestmentValuation: 'InvestmentValuation',
  InvestmentTransaction: 'InvestmentTransaction',
  Liability: 'Liability',
  LiabilityPayment: 'LiabilityPayment',
  SavingsGoal: 'SavingsGoal',
  SavingsProgress: 'SavingsProgress',
  ContractAnalysis: 'ContractAnalysis',
  Risk: 'Risk',
  Opportunity: 'Opportunity',
  CompensationStructure: 'CompensationStructure',
  Speech: 'Speech',
  Recording: 'Recording',
  Transcription: 'Transcription',
  Analysis: 'Analysis',
  VectorStore: 'VectorStore',
  Assistant: 'Assistant',
  Thread: 'Thread',
  Run: 'Run',
  Checkpoint: 'Checkpoint',
  CheckpointBlobs: 'CheckpointBlobs',
  CheckpointWrites: 'CheckpointWrites',
  CheckpointMigrations: 'CheckpointMigrations',
  Chat: 'Chat',
  Message: 'Message',
  MemoryStore: 'MemoryStore',
  Banner: 'Banner',
  Inventory: 'Inventory',
  Resident: 'Resident',
  Sale: 'Sale',
  SaleItem: 'SaleItem',
  StockMovement: 'StockMovement',
  PaymentRequest: 'PaymentRequest',
  PaymentRequestAccount: 'PaymentRequestAccount',
  PaymentAccount: 'PaymentAccount',
  Payment: 'Payment',
  MeetingReport: 'MeetingReport'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
