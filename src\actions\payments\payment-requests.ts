'use server'

import { prisma } from '@/lib/db'
import { revalidatePath } from 'next/cache'
import { PaymentAccountType } from '@/lib/types'

async function getPaymentRequest(id: string) {
    const requestWithJunction = await prisma.paymentRequest.findUnique({
        where: { id },
        include: {
            paymentAccounts: { include: { paymentAccount: true } },
            payments: { include: { remitterAccount: true, payeeAccount: true } }
        }
    });
    if (!requestWithJunction) return null;
    
    const flattenedRequest = {
        ...requestWithJunction,
        paymentAccounts: requestWithJunction.paymentAccounts.map(pra => pra.paymentAccount),
        payments: requestWithJunction.payments.map(p => ({ ...p, amount: p.amount }))
    };
    return flattenedRequest;
}

export async function createPaymentRequest(data: {
  name: string
  year: string
  month: string
  selectedPaymentRequestId?: string 
  copyFromPrevious?: boolean
}) {
  try {
    // Create the payment request with only valid database fields
    const { selectedPaymentRequestId, copyFromPrevious, ...paymentRequestData } = data;
    const paymentRequest = await prisma.paymentRequest.create({ 
      data: paymentRequestData 
    });
    
    // If copyFromPrevious is true, copy data from previous month
    if (copyFromPrevious) {
      await copyPreviousMonthData(paymentRequest.id, parseInt(data.year), parseInt(data.month), selectedPaymentRequestId);
    }
    
    const newRequest = await getPaymentRequest(paymentRequest.id);
    revalidatePath('/payment/services');
    return { success: true, data: newRequest };
  } catch (error: any) {
    console.error('Error creating payment request:', error);
    if (error.code === 'P2002') {
      return { success: false, error: 'Payment request for this year and month already exists' };
    }
    return { success: false, error: 'Failed to create payment request' };
  }
}

// Helper function to copy previous month data (add this to your server action file)
async function copyPreviousMonthData(
  newPaymentRequestId: string,
  currentYear: number,
  currentMonth: number,
  selectedPaymentRequestId?: string
) {
  try {
    // Calculate previous month and year
    let prevYear = currentYear;
    let prevMonth = currentMonth - 1;

    if (prevMonth <= 0) {
      prevMonth = 12;
      prevYear = currentYear - 1;
    }

    // Build where condition
    const whereCondition = selectedPaymentRequestId
      ? { id: selectedPaymentRequestId }
      : {
          year: prevYear.toString(),
          month: prevMonth.toString().padStart(2, "0"), // Ensure 2-digit format
        };

    // Find the previous payment request (either by selected ID or fallback prev year/month)
    const previousPaymentRequest = await prisma.paymentRequest.findFirst({
      where: whereCondition,
      include: {
        paymentAccounts: {
          include: {
            paymentAccount: true,
          },
        },
        payments: {
          include: {
            remitterAccount: true,
            payeeAccount: true,
          },
          orderBy: { sequenceId: "asc" },
        },
      },
    });

    if (!previousPaymentRequest) {
      if (selectedPaymentRequestId) {
        console.log(`No data found for selectedPaymentRequestId: ${selectedPaymentRequestId}`);
      } else {
        console.log(
          `No previous month data found for ${prevYear}/${prevMonth
            .toString()
            .padStart(2, "0")}`
        );
      }
      return;
    }

    // Copy payment accounts associations
    if (previousPaymentRequest.paymentAccounts.length > 0) {
      const paymentAccountsToCreate = previousPaymentRequest.paymentAccounts.map(
        (pra) => ({
          paymentRequestId: newPaymentRequestId,
          paymentAccountId: pra.paymentAccountId,
        })
      );

      await prisma.paymentRequestAccount.createMany({
        data: paymentAccountsToCreate,
      });
    }

    // Copy payments with updated sequence IDs and month
    if (previousPaymentRequest.payments.length > 0) {
      const paymentsToCreate = previousPaymentRequest.payments.map(
        (payment, index) => {
          // Generate new sequenceId: YYYYMM + sequential number (01, 02, 03...)
          const newSequenceId = `${currentYear}${currentMonth
            .toString()
            .padStart(2, "0")}${(index + 1).toString().padStart(2, "0")}`;

          // Update month field to current year/month
          const newMonth = `${currentYear}/${currentMonth
            .toString()
            .padStart(2, "0")}`;

          return {
            paymentRequestId: newPaymentRequestId,
            sequenceId: newSequenceId,
            accountingSubject: payment.accountingSubject,
            payee: payment.payee,
            remarks: payment.remarks,
            month: newMonth,
            amount: payment.amount,
            paymentMethod: payment.paymentMethod,
            remitterAccountId: payment.remitterAccountId,
            payeeAccountId: payment.payeeAccountId,
          };
        }
      );

      await prisma.payment.createMany({
        data: paymentsToCreate,
      });
    }

    console.log(
      `Successfully copied ${previousPaymentRequest.payments.length} payments and ${previousPaymentRequest.paymentAccounts.length} payment accounts from ${
        selectedPaymentRequestId
          ? `selectedPaymentRequestId: ${selectedPaymentRequestId}`
          : `${prevYear}/${prevMonth.toString().padStart(2, "0")}`
      } to ${currentYear}/${currentMonth.toString().padStart(2, "0")}`
    );
  } catch (error) {
    console.error("Error copying previous month data:", error);
    // Don't throw here - we want the payment request to be created even if copying fails
  }
}


export async function deletePaymentRequest(paymentRequestId: string) {
  try {
    // Delete related records first (due to foreign key constraints)
    
    // Delete payment request accounts junction records
    await prisma.paymentRequestAccount.deleteMany({
      where: {
        paymentRequestId: paymentRequestId
      }
    });
    
    // Delete payments associated with this payment request
    await prisma.payment.deleteMany({
      where: {
        paymentRequestId: paymentRequestId
      }
    });
    
    // Finally, delete the payment request itself
    const deletedPaymentRequest = await prisma.paymentRequest.delete({
      where: {
        id: paymentRequestId
      }
    });
    
    revalidatePath('/payment/services');
    return { 
      success: true, 
      message: 'Payment request deleted successfully',
      data: deletedPaymentRequest 
    };
  } catch (error: any) {
    console.error('Error deleting payment request:', error);
    
    if (error.code === 'P2025') {
      return { 
        success: false, 
        error: 'Payment request not found' 
      };
    }
    
    return { 
      success: false, 
      error: 'Failed to delete payment request' 
    };
  }
}

export async function createBankAccount(data: {
  accountType: PaymentAccountType
  bankCode: string
  bankName: string
  branchName: string
  accountNumber: string
  accountName: string
  isDefault: boolean
  isActive: boolean
  paymentRequestId: string
}) {
  try {
    const { paymentRequestId, ...accountData } = data;

    const paymentAccount = await prisma.paymentAccount.upsert({
      where: { 
        bankCode_accountNumber: {
          bankCode: accountData.bankCode,
          accountNumber: accountData.accountNumber,
        }
      },
      update: accountData,
      create: accountData,
    });

    await prisma.paymentRequestAccount.create({
      data: {
        paymentRequestId: paymentRequestId,
        paymentAccountId: paymentAccount.id,
      }
    });

    revalidatePath('/payment/services');
    return { success: true, data: paymentAccount };
  } catch (error: any) {
    console.error('Error linking bank account:', error);
    if (error.code === 'P2002') {
      return { success: false, error: 'Account already linked to this request' };
    }
    return { success: false, error: 'Failed to link bank account' };
  }
}

export async function updateBankAccount(id: string, data: any) {
  try {
    if (data.isDefault) {
      await prisma.paymentAccount.updateMany({ where: { isDefault: true, NOT: { id } }, data: { isDefault: false } });
    }
    const paymentAccount = await prisma.paymentAccount.update({ where: { id }, data });
    revalidatePath('/payment/services');
    return { success: true, data: paymentAccount };
  } catch (error) {
    console.error('Error updating bank account:', error);
    return { success: false, error: 'Failed to update bank account' };
  }
}

export async function unlinkBankAccount(paymentAccountId: string, paymentRequestId: string) {
  try {
    await prisma.paymentRequestAccount.delete({
      where: {
        paymentRequestId_paymentAccountId: {
          paymentRequestId,
          paymentAccountId,
        }
      }
    });
    revalidatePath('/payment/services');
    return { success: true };
  } catch (error) {
    console.error('Error unlinking bank account:', error);
    return { success: false, error: 'Failed to unlink bank account' };
  }
}

export async function deleteBankAccount(id: string) {
    console.warn('deleteBankAccount is deprecated and deletes a shared account. Use unlinkBankAccount instead.');
    try {
        await prisma.paymentAccount.delete({ where: { id } });
        revalidatePath('/payment/services');
        return { success: true };
    } catch (error) {
        console.error('Error deleting bank account:', error);
        return { success: false, error: 'Failed to delete bank account' };
    }
}

export async function createPayment(data: {
  sequenceId: string
  accountingSubject: string
  payee: string
  remarks?: string | null
  month: string
  amount: number
  paymentMethod: string
  showAccountInfo: boolean
  paymentRequestId: string
  remitterAccountId?: string
  payeeAccountId?: string
}) {
  try {
    const payment = await prisma.payment.create({
      data: {
        ...data,
        amount: Math.round(data.amount),
      },
      include: {
        remitterAccount: true,
        payeeAccount: true
      }
    });

    revalidatePath('/payment/services');
    return { 
      success: true, 
      data: {
        ...payment,
        amount: payment.amount
      }
    };
  } catch (error) {
    console.error('Error creating payment:', error);
    return { success: false, error: 'Failed to create payment' };
  }
}

export async function updatePayment(id: string, data: any) {
  try {
    const paymentData = { ...data };
    if (paymentData.amount) {
        paymentData.amount = Math.round(paymentData.amount);
    }

    const payment = await prisma.payment.update({
      where: { id },
      data: paymentData,
      include: {
        remitterAccount: true,
        payeeAccount: true
      }
    });

    revalidatePath('/payment/services');
    return { 
      success: true, 
      data: {
        ...payment,
        amount: payment.amount
      }
    };
  } catch (error) {
    console.error('Error updating payment:', error);
    return { success: false, error: 'Failed to update payment' };
  }
}

export async function deletePayment(id: string) {
  try {
    await prisma.payment.delete({ where: { id } });
    revalidatePath('/payment/services');
    return { success: true };
  } catch (error) {
    console.error('Error deleting payment:', error);
    return { success: false, error: 'Failed to delete payment' };
  }
}

export async function updatePaymentShowAccountInfo(
  id: string,
  { showAccountInfo }: { showAccountInfo: boolean }
) {
  try {
    const payment = await prisma.payment.updateMany({
      where: { paymentRequestId: id },
      data: { showAccountInfo },
    });

    revalidatePath("/payment/services");

    return {
      success: true,
      data: payment,
    };
  } catch (error) {
    console.error("Error updating payment:", error);
    return { success: false, error: "Failed to update payment" };
  }
}

