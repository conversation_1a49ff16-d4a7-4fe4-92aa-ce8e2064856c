"use server";

import { prisma } from "@/lib/db";
import { auth } from "@clerk/nextjs/server";
import { revalidatePath } from "next/cache";

export async function deleteReport(reportId: string, workspaceId: string) {
  const { userId } = await auth();
  if (!userId) {
    return { success: false, error: "Unauthorized" };
  }

  const member = await prisma.member.findFirst({
    where: { userId, workspaceId },
  });

  if (!member || member.role !== "ADMIN") {
    return { success: false, error: "Permission denied." };
  }

  try {
    // First, verify the report belongs to the workspace to prevent accidental cross-workspace deletion
    const report = await prisma.meetingReport.findFirst({
        where: {
            id: reportId,
            workspaceId: workspaceId,
        }
    });

    if (!report) {
        return { success: false, error: "Report not found in this workspace." };
    }

    await prisma.meetingReport.delete({
      where: {
        id: reportId,
      },
    });

    revalidatePath(`/workspaces/${workspaceId}/reports`);
    return { success: true };
  } catch (e) {
    console.error("Delete error:", e);
    return { success: false, error: "Failed to delete the report." };
  }
}
