"use server";

import { prisma } from "@/lib/db";
import { auth } from "@clerk/nextjs/server";
import { revalidatePath } from "next/cache";
import { z } from "zod";

// Zod schema now expects content as a string, not a file.
const UpdateReportSchema = z.object({
  title: z.string().min(3, "Title must be at least 3 characters long."),
  content: z.string().min(10, "Content is too short."),
  reportDate: z.coerce.date(),
  reportType: z.enum(["MONTHLY", "ANNUAL"]),
  visibility: z.enum(["PUBLIC", "WORKSPACE_MEMBERS"]),
  published: z.boolean(),
  workspaceId: z.string(),
  reportId: z.string(),
});


export async function updateReport(prevState: any, formData: FormData) {
  const { userId } = await auth();
  if (!userId) {
    return { success: false, error: "Unauthorized" };
  }
  
  const workspaceId = formData.get("workspaceId") as string;
  const reportId = formData.get("reportId") as string;

  const member = await prisma.member.findFirst({
    where: { userId, workspaceId: workspaceId },
  });
  if (!member || member.role !== "ADMIN") {
     return { success: false, error: "Permission denied. You must be an admin." };
  }

  const validatedFields = UpdateReportSchema.safeParse({
    title: formData.get("title"),
    content: formData.get("content"), // Get content from form
    reportDate: formData.get("reportDate"),
    reportType: formData.get("reportType"),
    visibility: formData.get("visibility"),
    published: formData.get("published") === "on",
    workspaceId: workspaceId,
    reportId: reportId,
  });

  if (!validatedFields.success) {
    return {
      success: false,
      error: "Invalid form data.",
      issues: validatedFields.error.flatten().fieldErrors,
    };
  }

  const { title, content, reportDate, reportType, visibility, published } = validatedFields.data;

  try {
    const report = await prisma.meetingReport.findFirst({
        where: { id: reportId, workspaceId: workspaceId }
    });
    if (!report) {
        return { success: false, error: "Report not found." };
    }

    await prisma.meetingReport.update({
      where: { id: reportId },
      data: {
        title,
        reportDate,
        reportType,
        visibility,
        published,
        content, // Update content directly
      },
    });

    revalidatePath(`/workspaces/${workspaceId}/reports`);
    revalidatePath(`/workspaces/${workspaceId}/reports/${report.slug}`);
    return { success: true, updatedSlug: report.slug };
  } catch (e) {
    console.error(e);
    return { success: false, error: "Failed to update the report." };
  }
}
