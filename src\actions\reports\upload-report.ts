"use server";

import { prisma } from "@/lib/db";
import { auth } from "@clerk/nextjs/server";
import { revalidatePath } from "next/cache";
import { z } from "zod";
import mammoth from "mammoth";

const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
const ACCEPTED_FILE_TYPES = ["text/html", "application/vnd.openxmlformats-officedocument.wordprocessingml.document"];

// Zod schema for validation
const ReportSchema = z.object({
  title: z.string().min(3, "Title must be at least 3 characters long."),
  reportFile: z
    .instanceof(File)
    .refine((file) => file.size > 0, "Please upload a file.")
    .refine(
      (file) => file.size <= MAX_FILE_SIZE,
      `File size should be less than 5MB.`
    )
    .refine(
      (file) => ACCEPTED_FILE_TYPES.includes(file.type),
      "Invalid file type. Please upload an HTML or DOCX file."
    ),
  reportDate: z.coerce.date(),
  reportType: z.enum(["MONTHLY", "ANNUAL"]),
  visibility: z.enum(["PUBLIC", "WORKSPACE_MEMBERS"]),
  workspaceId: z.string(),
});

// Helper to generate a slug
function generateSlug(title: string) {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, "")
    .replace(/\s+/g, "-")
    .slice(0, 50);
}

export async function uploadReport(prevState: any, formData: FormData) {
  const { userId } = await auth();
  if (!userId) {
    return { success: false, error: "Unauthorized" };
  }
  
  const workspaceId = formData.get("workspaceId") as string;

  // Check if user is an admin of the workspace
  const member = await prisma.member.findFirst({
    where: { userId, workspaceId: workspaceId },
  });
  if (!member || member.role !== "ADMIN") {
     return { success: false, error: "Permission denied. You must be an admin." };
  }

  const validatedFields = ReportSchema.safeParse({
    title: formData.get("title"),
    reportFile: formData.get("reportFile"),
    reportDate: formData.get("reportDate"),
    reportType: formData.get("reportType"),
    visibility: formData.get("visibility"),
    workspaceId: workspaceId,
  });

  if (!validatedFields.success) {
    return {
      success: false,
      error: "Invalid form data.",
      issues: validatedFields.error.flatten().fieldErrors,
    };
  }

  const { title, reportFile, reportDate, reportType, visibility } = validatedFields.data;

  let htmlContent: string;

  try {
    // Process the file
    if (reportFile.type === "text/html") {
      htmlContent = await reportFile.text();
    } else if (reportFile.type === "application/vnd.openxmlformats-officedocument.wordprocessingml.document") {
      const buffer = await reportFile.arrayBuffer();
      const result = await mammoth.convertToHtml({ buffer: Buffer.from(buffer) });
      htmlContent = result.value;
    } else {
      // This case should technically be caught by Zod, but as a fallback:
      return { success: false, error: "Unsupported file type." };
    }
  } catch (conversionError) {
    console.error("File conversion error:", conversionError);
    return { success: false, error: "Failed to process the uploaded file. It may be corrupt or in an unexpected format." };
  }


  if (!htmlContent || htmlContent.length < 10) {
      return { success: false, error: "The processed file content is too short or empty." };
  }

  // Generate a unique slug
  let slug = generateSlug(title);
  const existingReport = await prisma.meetingReport.findUnique({ where: { slug } });
  if (existingReport) {
    slug = `${slug}-${Date.now()}`; // Append timestamp to ensure uniqueness
  }

  try {
    await prisma.meetingReport.create({
      data: {
        title,
        slug,
        content: htmlContent, // Save the processed HTML
        reportDate,
        reportType,
        visibility,
        workspaceId,
        userId,
      },
    });

    revalidatePath(`/workspaces/${workspaceId}/reports`);
    return { success: true };
  } catch (e) {
    console.error(e);
    return { success: false, error: "Failed to create report in the database." };
  }
}
