"use client";

import React, { useState, useMemo } from "react";
import {
  Plus,
  Minus,
  Package,
  Users,
  Calendar,
  FileText,
  RefreshCw,
  Edit,
  Trash2,
} from "lucide-react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
  getInventory,
  getSalesHistory,
  processSale as apiProcessSale,
  updateInventoryStock as apiUpdateStock,
  isSuperUser as apiIsSuperUser,
  deleteSale as apiDeleteSale,
  updateSale as apiUpdateSale,
} from "@/actions/inventory/actions";
import {
  ItemType,
  ItemTypeDisplay,
  SaleRecord,
  SaleFormData,
} from "@/types/inventory";
import { toast } from "sonner";

type InventoryState = Record<ItemType, { current: number; price: number }>;

const EditSaleModal: React.FC<{
  sale: SaleRecord;
  inventory: InventoryState;
  onClose: () => void;
  onSave: (data: SaleFormData) => Promise<void>;
  isSaving: boolean;
}> = ({ sale, inventory, onClose, onSave, isSaving }) => {
  const [form, setForm] = useState<SaleFormData>(() => {
    const saleItem = sale.saleItems[0];
    return {
      date: new Date(sale.date).toISOString().split("T")[0],
      residentName: sale.residentName || "",
      address: sale.address || "",
      receiptNo: sale.receiptNo || "",
      itemType: saleItem.itemType,
      quantity: saleItem.quantity,
      unitPrice: saleItem.unitPrice,
      recipient: sale.recipient || "",
    };
  });

  const handleChange = <K extends keyof SaleFormData>(
    field: K,
    value: SaleFormData[K]
  ) => {
    setForm((prev) => {
      const updated = { ...prev, [field]: value };
      if (field === "itemType") {
        updated.unitPrice = inventory[value as ItemType]?.price || 0;
      }
      return updated;
    });
  };

  const itemTypes = Object.keys(inventory) as ItemType[];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center">
      <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-2xl">
        <h2 className="text-2xl font-bold mb-4">Edit Sale</h2>
        <div className="grid md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              購買日期
            </label>
            <input
              type="date"
              value={form.date}
              onChange={(e) => handleChange("date", e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              住戶姓名 *
            </label>
            <input
              type="text"
              value={form.residentName}
              onChange={(e) => handleChange("residentName", e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              地址 *
            </label>
            <input
              type="text"
              value={form.address}
              onChange={(e) => handleChange("address", e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              購買項目
            </label>
            <select
              value={form.itemType}
              onChange={(e) =>
                handleChange("itemType", e.target.value as ItemType)
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
            >
              {itemTypes.map((item) => (
                <option key={item} value={item}>
                  {ItemTypeDisplay[item]}
                </option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              購買數量
            </label>
            <input
              type="number"
              min="1"
              value={form.quantity}
              onChange={(e) =>
                handleChange("quantity", parseInt(e.target.value) || 1)
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              單價
            </label>
            <input
              type="number"
              min="0"
              value={form.unitPrice}
              onChange={(e) =>
                handleChange("unitPrice", parseInt(e.target.value) || 0)
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
            />
          </div>
        </div>
        <div className="mt-6 flex justify-end gap-4">
          <button
            onClick={onClose}
            disabled={isSaving}
            className="px-4 py-2 bg-gray-200 rounded-md disabled:opacity-50"
          >
            Cancel
          </button>
          <button
            onClick={() => onSave(form)}
            disabled={isSaving}
            className="px-4 py-2 bg-blue-600 text-white rounded-md disabled:opacity-50"
          >
            {isSaving ? "Saving..." : "Save Changes"}
          </button>
        </div>
      </div>
    </div>
  );
};

const MagneticCardInventory: React.FC = () => {
  const queryClient = useQueryClient();
  const [editingSale, setEditingSale] = useState<SaleRecord | null>(null);

  const { data: inventoryData, isLoading: isInventoryLoading } = useQuery({
    queryKey: ["inventory"],
    queryFn: getInventory,
  });
  const { data: salesHistory = [], isLoading: isSalesLoading } = useQuery({
    queryKey: ["salesHistory"],
    queryFn: () => getSalesHistory(),
  });
  const { data: isSuperUser = false } = useQuery({
    queryKey: ["isSuperUser"],
    queryFn: apiIsSuperUser,
  });

  const inventoryMap = useMemo(() => {
    if (!inventoryData) return {} as InventoryState;
    return inventoryData.reduce((acc, item) => {
      acc[item.itemType] = { current: item.current, price: item.price };
      return acc;
    }, {} as InventoryState);
  }, [inventoryData]);

  const [saleForm, setSaleForm] = useState<SaleFormData>({
    date: new Date().toISOString().split("T")[0],
    residentName: "",
    address: "",
    receiptNo: "",
    itemType: ItemType.A_BUILDING_CARD,
    quantity: 1,
    unitPrice: 200,
    recipient: "",
  });

  const handleMutationSuccess = () => {
    queryClient.invalidateQueries({ queryKey: ["inventory"] });
    queryClient.invalidateQueries({ queryKey: ["salesHistory"] });
  };

  const processSaleMutation = useMutation({
    mutationFn: apiProcessSale,
    onSuccess: () => {
      handleMutationSuccess();
      toast.success("銷售記錄已完成！");
      setSaleForm({
        date: new Date().toISOString().split("T")[0],
        residentName: "",
        address: "",
        receiptNo: "",
        itemType: ItemType.A_BUILDING_CARD,
        quantity: 1,
        unitPrice: inventoryMap[ItemType.A_BUILDING_CARD]?.price || 200,
        recipient: "",
      });
    },
    onError: (error: Error) => toast.error(`處理失敗: ${error.message}`),
  });

  const updateStockMutation = useMutation({
    mutationFn: (variables: { itemType: ItemType; amount: number }) =>
      apiUpdateStock(variables.itemType, variables.amount),
    onSuccess: handleMutationSuccess,
    onError: (error: Error) => toast.error(`更新庫存失敗: ${error.message}`),
  });
  const deleteSaleMutation = useMutation({
    mutationFn: apiDeleteSale,
    onSuccess: () => {
      handleMutationSuccess();
      toast.success("Sale deleted successfully.");
    },
    onError: (error: Error) =>
      toast.error(`Failed to delete sale: ${error.message}`),
  });
  const updateSaleMutation = useMutation({
    mutationFn: (variables: { saleId: string; data: SaleFormData }) =>
      apiUpdateSale(variables.saleId, variables.data),
    onSuccess: () => {
      handleMutationSuccess();
      setEditingSale(null);
      toast.success("Sale updated successfully.");
    },
    onError: (error: Error) =>
      toast.error(`Failed to update sale: ${error.message}`),
  });

  const handleFormChange = <K extends keyof SaleFormData>(
    field: K,
    value: SaleFormData[K]
  ): void => {
    setSaleForm((prev) => {
      const updated = { ...prev, [field]: value };
      if (field === "itemType") {
        updated.unitPrice = inventoryMap[value as ItemType]?.price || 0;
      }
      return updated;
    });
  };

  const processSale = () => {
    const { itemType, quantity, residentName, address } = saleForm;
    if (!residentName.trim() || !address.trim())
      return toast.error("請填寫住戶姓名和地址");
    if (!inventoryMap[itemType] || inventoryMap[itemType].current < quantity)
      return toast.error(
        `庫存不足！目前${ItemTypeDisplay[itemType]}剩餘：${inventoryMap[itemType]?.current || 0}個`
      );
    processSaleMutation.mutate(saleForm);
  };

  const handleDelete = (saleId: string) => {
    if (
      window.confirm(
        "Are you sure you want to delete this sale record? This will also restock the inventory. This action cannot be undone."
      )
    ) {
      deleteSaleMutation.mutate(saleId);
    }
  };

  const handleUpdateSale = async (data: SaleFormData) => {
    if (!editingSale) return;
    updateSaleMutation.mutate({ saleId: editingSale.id, data });
  };

  const getTotalValue = (): number =>
    Object.values(inventoryMap).reduce(
      (total, data) => total + data.current * data.price,
      0
    );
  const getInventoryEntries = (): [
    ItemType,
    { current: number; price: number },
  ][] =>
    Object.entries(inventoryMap) as [
      ItemType,
      { current: number; price: number },
    ][];
  const getItemTypes = (): ItemType[] =>
    Object.keys(inventoryMap) as ItemType[];

  if (isInventoryLoading || isSalesLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <RefreshCw className={`text-gray-600 animate-spin`} />
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6 bg-gray-50 min-h-screen">
      {editingSale && (
        <EditSaleModal
          sale={editingSale}
          inventory={inventoryMap}
          onClose={() => setEditingSale(null)}
          onSave={handleUpdateSale}
          isSaving={updateSaleMutation.isPending}
        />
      )}
      <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
        <div className="flex justify-between items-center mb-2">
          <h1 className="text-3xl font-bold text-gray-800">
            僑星福華社區磁扣庫存管理系統
          </h1>
          <button
            onClick={() => queryClient.invalidateQueries()}
            className="p-2 rounded-full hover:bg-gray-100 transition-colors"
          >
            <RefreshCw
              className={`text-gray-600 ${queryClient.isFetching() ? "animate-spin" : ""}`}
            />
          </button>
        </div>
        <p className="text-center text-gray-600 mb-6">
          Magnetic Card & Remote Control Inventory Management
        </p>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
          {getInventoryEntries().map(([item, data]) => (
            <div
              key={item}
              className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg p-4 border border-blue-200"
            >
              <div className="flex items-center justify-between mb-2">
                <h3 className="font-bold text-gray-800">
                  {ItemTypeDisplay[item]}
                </h3>
                <Package className="text-blue-600" size={20} />
              </div>
              <div className="text-2xl font-bold text-blue-600 mb-2">
                {data.current}
              </div>
              <div className="text-sm text-gray-600 mb-3">
                單價: ${data.price.toLocaleString()}
              </div>
              <div className="flex gap-2">
                <button
                  onClick={() =>
                    updateStockMutation.mutate({ itemType: item, amount: 1 })
                  }
                  className="flex-1 bg-green-500 text-white px-2 py-1 rounded text-sm hover:bg-green-600 flex items-center justify-center gap-1 transition-colors"
                >
                  <Plus size={14} /> 補貨
                </button>
                <button
                  onClick={() =>
                    data.current > 0 &&
                    updateStockMutation.mutate({ itemType: item, amount: -1 })
                  }
                  disabled={data.current === 0}
                  className="flex-1 bg-red-500 text-white px-2 py-1 rounded text-sm hover:bg-red-600 flex items-center justify-center gap-1 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
                >
                  <Minus size={14} /> 調整
                </button>
              </div>
            </div>
          ))}
        </div>

        <div className="bg-gradient-to-r from-green-100 to-blue-100 rounded-lg p-4 mb-8">
          <div className="flex items-center justify-between">
            <span className="text-lg font-semibold text-gray-800">
              總庫存價值
            </span>
            <span className="text-2xl font-bold text-green-600">
              ${getTotalValue().toLocaleString()}
            </span>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
        <h2 className="text-2xl font-bold text-gray-800 mb-4 flex items-center gap-2">
          <Users className="text-blue-600" />
          住戶購買登記
        </h2>
        <div className="grid md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                購買日期
              </label>
              <input
                type="date"
                value={saleForm.date}
                onChange={(e) => handleFormChange("date", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                住戶姓名 *
              </label>
              <input
                type="text"
                value={saleForm.residentName}
                onChange={(e) =>
                  handleFormChange("residentName", e.target.value)
                }
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                placeholder="請輸入住戶姓名"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                地址 *
              </label>
              <input
                type="text"
                value={saleForm.address}
                onChange={(e) => handleFormChange("address", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                placeholder="例: A棟6樓之1"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                收據編號
              </label>
              <input
                type="tel"
                value={saleForm.receiptNo}
                onChange={(e) => handleFormChange("receiptNo", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                placeholder="請輸入收據編號"
              />
            </div>
          </div>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                購買項目
              </label>
              <select
                value={saleForm.itemType}
                onChange={(e) =>
                  handleFormChange("itemType", e.target.value as ItemType)
                }
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              >
                {getItemTypes().map((item) => (
                  <option key={item} value={item}>
                    {ItemTypeDisplay[item]} (庫存:{" "}
                    {inventoryMap[item]?.current || 0})
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                購買數量
              </label>
              <input
                type="number"
                min="1"
                max={inventoryMap[saleForm.itemType]?.current || 0}
                value={saleForm.quantity}
                onChange={(e) =>
                  handleFormChange(
                    "quantity",
                    Math.max(1, parseInt(e.target.value) || 1)
                  )
                }
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                單價
              </label>
              <input
                type="number"
                min="0"
                value={saleForm.unitPrice}
                onChange={(e) =>
                  handleFormChange(
                    "unitPrice",
                    Math.max(0, parseInt(e.target.value) || 0)
                  )
                }
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                收款人 *
              </label>
              <textarea
                value={saleForm.recipient}
                onChange={(e) => handleFormChange("recipient", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md resize-none"
                rows={2}
                placeholder="收款人簽章"
                required
              />
            </div>
          </div>
        </div>
        <div className="mt-6 flex justify-between items-center">
          <div className="text-lg font-semibold text-gray-800">
            總金額:{" "}
            <span className="text-green-600">
              ${(saleForm.quantity * saleForm.unitPrice).toLocaleString()}
            </span>
          </div>
          <button
            onClick={processSale}
            disabled={processSaleMutation.isPending}
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2 transition-colors disabled:bg-gray-400"
          >
            <FileText size={20} />
            {processSaleMutation.isPending ? "Processing..." : "確認購買"}
          </button>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-lg p-6">
        <h2 className="text-2xl font-bold text-gray-800 mb-4 flex items-center gap-2">
          <Calendar className="text-green-600" />
          銷售記錄 ({salesHistory.length})
        </h2>
        <div className="overflow-x-auto">
          <table className="w-full table-auto">
            <thead>
              <tr className="bg-gray-100">
                <th className="px-4 py-2 text-left font-medium text-gray-700">
                  日期
                </th>
                <th className="px-4 py-2 text-left font-medium text-gray-700">
                  住戶
                </th>
                <th className="px-4 py-2 text-left font-medium text-gray-700">
                  地址
                </th>
                <th className="px-4 py-2 text-left font-medium text-gray-700">
                  項目
                </th>
                <th className="px-4 py-2 text-left font-medium text-gray-700">
                  總額
                </th>
                {isSuperUser && (
                  <th className="px-4 py-2 text-left font-medium text-gray-700">
                    Actions
                  </th>
                )}
              </tr>
            </thead>
            <tbody>
              {(salesHistory as SaleRecord[]).map((sale) => (
                <tr key={sale.id} className={"bg-gray-50"}>
                  <td className="px-4 py-2 text-sm">
                    {new Date(sale.date).toLocaleDateString()}
                  </td>
                  <td className="px-4 py-2 text-sm font-medium">
                    {sale.residentName}
                  </td>
                  <td className="px-4 py-2 text-sm">{sale.address}</td>
                  <td className="px-4 py-2 text-sm">
                    {sale.saleItems
                      .map((i) => ItemTypeDisplay[i.itemType])
                      .join(", ")}
                  </td>
                  <td className="px-4 py-2 text-sm font-medium text-green-600">
                    ${sale.totalAmount.toLocaleString()}
                  </td>
                  {isSuperUser && (
                    <td className="px-4 py-2 text-sm flex items-center">
                      <button
                        onClick={() => setEditingSale(sale)}
                        disabled={
                          deleteSaleMutation.isPending ||
                          updateSaleMutation.isPending
                        }
                        className="p-1 text-blue-600 hover:text-blue-800 disabled:opacity-50"
                      >
                        <Edit size={16} />
                      </button>
                      <button
                        onClick={() => handleDelete(sale.id)}
                        disabled={
                          deleteSaleMutation.isPending ||
                          updateSaleMutation.isPending
                        }
                        className="p-1 text-red-600 hover:text-red-800 ml-2 disabled:opacity-50"
                      >
                        <Trash2 size={16} />
                      </button>
                    </td>
                  )}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default MagneticCardInventory;
