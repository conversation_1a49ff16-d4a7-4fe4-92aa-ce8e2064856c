import { prisma } from "@/lib/db";
import { auth } from "@clerk/nextjs/server";
import { notFound, redirect } from "next/navigation";
import { EditReportForm } from "@/components/reports/edit-report-form";

async function getReportForEdit(slug: string, workspaceId: string, userId: string) {
    const member = await prisma.member.findFirst({
        where: { userId, workspaceId },
    });

    if (!member || member.role !== "ADMIN") {
        return null;
    }

    const report = await prisma.meetingReport.findFirst({
        where: { slug, workspaceId },
    });

    return report;
}


export default async function EditReportPage({
  params,
}: {
  params: Promise<{ workspaceId: string; slug: string }>;
}) {
  const { userId } = await auth();
  if (!userId) {
    redirect("/sign-in");
  }

  const { workspaceId, slug } = await params;
  const report = await getReportForEdit(slug, workspaceId, userId);

  if (!report) {
    notFound();
  }

  return (
    <div className="flex flex-col items-center container mx-auto">
      <h1 className="text-3xl font-bold">Edit Report</h1>
      <EditReportForm key={report.id} report={report} />
    </div>
  );
}
