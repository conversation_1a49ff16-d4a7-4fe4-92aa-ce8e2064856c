"server only";

import { prisma } from "@/lib/db";
import { auth } from "@clerk/nextjs/server";
import { notFound, redirect } from "next/navigation";
import { EditReportButton } from "@/components/reports/edit-report-button";

// Helper to check membership
async function checkWorkspaceMembership(userId: string, workspaceId: string) {
  const member = await prisma.member.findUnique({
    where: {
      userId_workspaceId: {
        userId,
        workspaceId,
      },
    },
  });
  return !!member;
}

export default async function ReportDetailPage({
  params,
}: {
  params: Promise<{ workspaceId: string; slug: string }>;
}) {
  const { userId } = await auth();
  if (!userId) {
    redirect("/sign-in");
  }

  const { workspaceId, slug } = await params;
  const hasAccess = await checkWorkspaceMembership(userId, workspaceId);
  if (!hasAccess) {
    notFound();
  }

  const report = await prisma.meetingReport.findUnique({
    where: {
      slug: slug,
      workspaceId: workspaceId, // Ensure report belongs to the workspace
    },
  });

  if (!report) {
    notFound();
  }

  return (
    <div className="w-full mx-auto">
      <div className="max-w-8xl bg-white p-4 md:p-6 rounded-lg shadow mx-auto">
        <div className="max-w-4xl mx-auto flex items-center justify-between mb-6">
        <div>
          <h1 className="text-3xl md:text-4xl font-bold tracking-tight mb-2">{report.title}</h1>
        </div>
        <EditReportButton workspaceId={workspaceId} slug={report.slug} />
      </div>
      <div
          className="prose dark:prose-invert max-w-none"
          dangerouslySetInnerHTML={{ __html: report.content }}
        ></div>
      </div>
    </div>
  );
}
