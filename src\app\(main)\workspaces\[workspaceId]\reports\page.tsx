"use server";
import { prisma } from "@/lib/db"; 
import { auth } from "@clerk/nextjs/server";
import { notFound, redirect } from "next/navigation";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { PlusCircle, Edit2 } from "lucide-react";
import { DeleteReportButton } from "@/components/reports/delete-report-button";

// Helper to check membership and get role
async function getWorkspaceMember(userId: string, workspaceId: string) {
  return await prisma.member.findUnique({
    where: {
      userId_workspaceId: {
        userId,
        workspaceId,
      },
    },
  });
}

export default async function WorkspaceReportsPage({
  params,
}: {
  params: Promise<{ workspaceId: string }>;
}) {
  const { userId } = await auth();
  if (!userId) {
    redirect("/sign-in");
  }

  const workspaceId = (await params).workspaceId;
  if (!workspaceId) {
    notFound();
  }


  const member = await getWorkspaceMember(userId, workspaceId);
  if (!member) {
    notFound();
  }
  const isAdmin = member.role === 'ADMIN';

  const reports = await prisma.meetingReport.findMany({
    where: {
      workspaceId: workspaceId,
    },
    orderBy: {
      reportDate: "desc",
    },
    select: {
      id: true,
      title: true,
      slug: true,
      reportDate: true,
      reportType: true,
    },
  });

  return (
    <div className="container mx-auto py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Workspace Reports</h1>
        {isAdmin && (
          <Button asChild>
            <Link href={`/workspaces/${workspaceId}/reports/upload`}>
              <PlusCircle className="mr-2 h-4 w-4" />
              Upload Report
            </Link>
          </Button>
        )}
      </div>
      <div className="bg-white shadow rounded-lg">
        <ul className="divide-y divide-gray-200">
          {reports.length > 0 ? (
            reports.map((report) => (
              <li key={report.id} className="flex items-center justify-between p-4 sm:p-6">
                <Link href={`/workspaces/${workspaceId}/reports/${report.slug}`} className="flex-grow block hover:bg-gray-50 -m-4 sm:-m-6 p-4 sm:p-6 rounded-l-lg">
                  <div className="flex items-center justify-between">
                    <p className="text-lg font-medium text-indigo-600 truncate">{report.title}</p>
                    <div className="ml-2 flex-shrink-0 flex">
                      <p className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        report.reportType === 'ANNUAL' ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'
                      }`}>
                        {report.reportType}
                      </p>
                    </div>
                  </div>
                  <div className="mt-2 sm:flex sm:justify-between">
                    <div className="sm:flex">
                      <p className="flex items-center text-sm text-gray-500">
                        {new Date(report.reportDate).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                </Link>
                {isAdmin && (
                  <div className="ml-4 flex-shrink-0">
                    <Button variant="ghost" size="sm" className="hover:cursor-pointer" asChild>
                      <Link href={`/workspaces/${workspaceId}/reports/${report.slug}/edit`}>
                        <Edit2 size={"20"} color="blue" />
                      </Link>
                    </Button>
                    <DeleteReportButton reportId={report.id} workspaceId={workspaceId} />
                  </div>
                )}
              </li>
            ))
          ) : (
            <li className="p-6 text-center text-gray-500">
              No reports found for this workspace.
            </li>
          )}
        </ul>
      </div>
    </div>
  );
}
