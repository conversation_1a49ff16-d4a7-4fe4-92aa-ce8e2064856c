"use client";

import { use, useEffect, useActionState } from "react";
import { useFormStatus } from "react-dom";
import { uploadReport } from "@/actions/reports/upload-report";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "sonner";
import { useRouter } from "next/navigation";

function SubmitButton() {
  const { pending } = useFormStatus();
  
  return (
    <Button type="submit" disabled={pending}>
      {pending ? "Uploading..." : "Upload Report"}
    </Button>
  );
}

export default function UploadReportPage({
  params,
}: {
  params: Promise<{ workspaceId: string }>;
}) {
  // Unwrap the params Promise using React.use()
  const { workspaceId } = use(params);
  
  const initialState = { success: false, error: undefined, issues: undefined };
  const [state, dispatch] = useActionState(uploadReport, initialState);
  const router = useRouter();

  useEffect(() => {
    if (state.success) {
      toast.success("Report uploaded successfully.");
      router.push(`/workspaces/${workspaceId}/reports`);
    } else if (state.error) {
      toast.error(state.error);
    }
  }, [state, router, workspaceId]); // Updated dependency array

  return (
    <div className="container mx-auto px-0 py-8">
      <h1 className="text-3xl font-bold mb-6">Upload New Report</h1>
      <form action={dispatch} className="space-y-6 max-w-2xl">
        <input type="hidden" name="workspaceId" value={workspaceId} />
        
        <div className="space-y-2">
          <Label htmlFor="title">Report Title</Label>
          <Input id="title" name="title" required />
          {state.issues?.title && <p className="text-sm text-red-500">{state.issues.title[0]}</p>}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <Label htmlFor="reportDate">Report Date</Label>
            <Input id="reportDate" name="reportDate" type="date" required className="hover:cursor-pointer" />
            {state.issues?.reportDate && <p className="text-sm text-red-500">{state.issues.reportDate[0]}</p>}
          </div>
          <div className="space-y-2">
            <Label htmlFor="reportType">Report Type</Label>
            <Select name="reportType" defaultValue="MONTHLY">
              <SelectTrigger>
                <SelectValue placeholder="Select a type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="MONTHLY">Monthly</SelectItem>
                <SelectItem value="ANNUAL">Annual</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="visibility">Visibility</Label>
          <Select name="visibility" defaultValue="WORKSPACE_MEMBERS">
            <SelectTrigger>
              <SelectValue placeholder="Select visibility" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="WORKSPACE_MEMBERS">Workspace Members Only</SelectItem>
              <SelectItem value="PUBLIC">Public</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="reportFile">Report File</Label>
          <Input
            id="reportFile"
            name="reportFile"
            type="file"
            accept=".html,.docx"
            required
            className="hover:cursor-pointer"
          />
          <p className="text-sm text-muted-foreground">
            Please upload an HTML or DOCX file.
          </p>
          {state.issues?.reportFile && <p className="text-sm text-red-500">{state.issues.reportFile[0]}</p>}
        </div>

        <SubmitButton />
      </form>
    </div>
  );
}