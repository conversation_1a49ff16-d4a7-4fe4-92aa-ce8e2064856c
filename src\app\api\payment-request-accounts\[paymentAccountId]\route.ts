import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'

export async function DELETE(
  request: NextRequest,
  { params }: { params: { paymentAccountId: string } }
) {
  try {
    const { searchParams } = new URL(request.url);
    const paymentRequestId = searchParams.get('paymentRequestId');

    if (!paymentRequestId) {
        return NextResponse.json({ error: 'paymentRequestId is required' }, { status: 400 });
    }

    await prisma.paymentRequestAccount.delete({
      where: {
        paymentRequestId_paymentAccountId: {
          paymentRequestId: paymentRequestId,
          paymentAccountId: params.paymentAccountId,
        }
      }
    });

    return new NextResponse(null, { status: 204 });
  } catch (error) {
    console.error('Error unlinking payment account:', error);
    return NextResponse.json({ error: 'Failed to unlink payment account' }, { status: 500 });
  }
}