import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'

export async function GET() {
  try {
    const paymentRequestsWithJunction = await prisma.paymentRequest.findMany({
      include: {
        paymentAccounts: {
          include: {
            paymentAccount: true
          }
        },
        payments: {
          include: {
            remitterAccount: true,
            payeeAccount: true
          },
          orderBy: { sequenceId: 'asc' }
        }
      },
      orderBy: [
        { year: 'desc' },
        { month: 'desc' },
        { createdAt: 'desc' }
      ]
    });

    const paymentRequests = paymentRequestsWithJunction.map(pr => ({
      ...pr,
      paymentAccounts: pr.paymentAccounts.map(pra => pra.paymentAccount),
      payments: pr.payments.map(p => ({ ...p, amount: p.amount }))
    }));
    
    return NextResponse.json(paymentRequests)
  } catch (error) {
    console.error('Error fetching payment requests:', error)
    return NextResponse.json(
      { error: 'Failed to fetch payment requests' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const { name, year, month, copyFromPrevious } = await request.json()
    
    // Create the new payment request first (exclude copyFromPrevious from data)
    const paymentRequest = await prisma.paymentRequest.create({
      data: { name, year, month }
    })
    
    // If copyFromPrevious is true, find and copy data from previous month
    if (copyFromPrevious) {
      await copyPreviousMonthData(paymentRequest.id, year, month);
    }
    
    // Fetch the complete payment request with all relations
    const newPaymentRequest = await prisma.paymentRequest.findUnique({
      where: { id: paymentRequest.id },
      include: {
        paymentAccounts: { include: { paymentAccount: true } },
        payments: { 
          include: { 
            remitterAccount: true, 
            payeeAccount: true 
          },
          orderBy: { sequenceId: 'asc' }
        }
      }
    });

    const responseData = {
      ...newPaymentRequest,
      paymentAccounts: newPaymentRequest!.paymentAccounts.map(pra => pra.paymentAccount),
      payments: newPaymentRequest!.payments.map(p => ({ ...p, amount: p.amount }))
    }
    
    return NextResponse.json({ success: true, data: responseData }, { status: 201 })
  } catch (error: any) {
    console.error('Error creating payment request:', error)
    
    if (error.code === 'P2002') {
      return NextResponse.json(
        { error: 'Payment request for this year and month already exists' },
        { status: 409 }
      )
    } else {
      return NextResponse.json(
        { error: 'Failed to create payment request' },
        { status: 500 }
      )
    }
  }
}

// Helper function to copy previous month data
async function copyPreviousMonthData(newPaymentRequestId: string, currentYear: number, currentMonth: number) {
  try {
    // Calculate previous month and year
    let prevYear = currentYear;
    let prevMonth = currentMonth - 1;
    
    if (prevMonth <= 0) {
      prevMonth = 12;
      prevYear = currentYear - 1;
    }
    
    // Find the previous month's payment request
    const previousPaymentRequest = await prisma.paymentRequest.findFirst({
      where: {
        year: String(prevYear),
        month: String(prevMonth)
      },
      include: {
        paymentAccounts: {
          include: {
            paymentAccount: true
          }
        },
        payments: {
          include: {
            remitterAccount: true,
            payeeAccount: true
          },
          orderBy: { sequenceId: 'asc' }
        }
      }
    });
    
    if (!previousPaymentRequest) {
      console.log('No previous month data found to copy');
      return;
    }
    
    // Copy payment accounts associations
    if (previousPaymentRequest.paymentAccounts.length > 0) {
      const paymentAccountsToCreate = previousPaymentRequest.paymentAccounts.map(pra => ({
        paymentRequestId: newPaymentRequestId,
        paymentAccountId: pra.paymentAccountId
      }));
      
      await prisma.paymentRequestAccount.createMany({
        data: paymentAccountsToCreate
      });
    }
    
    // Copy payments with updated sequence IDs and month info
    if (previousPaymentRequest.payments.length > 0) {
      const paymentsToCreate = previousPaymentRequest.payments.map(payment => ({
        paymentRequestId: newPaymentRequestId,
        sequenceId: payment.sequenceId,
        accountingSubject: payment.accountingSubject,
        payee: payment.payee,
        remarks: payment.remarks,
        month: `${prevYear}/${prevMonth}`,
        amount: payment.amount, // Keep original amount (in cents)
        paymentMethod: payment.paymentMethod,
        remitterAccountId: payment.remitterAccountId,
        payeeAccountId: payment.payeeAccountId,
      }));
      
      await prisma.payment.createMany({
        data: paymentsToCreate
      });
    }
    
    console.log(`Successfully copied data from ${prevYear}/${prevMonth} to ${currentYear}/${currentMonth}`);
    
  } catch (error) {
    console.error('Error copying previous month data:', error);
    // Don't throw here - we want the payment request to be created even if copying fails
  }
}