import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'

export async function POST(request: NextRequest) {
  try {
    const { accountData, paymentRequestId } = await request.json();
    const { accountName, accountNumber, bankCode, bankName, branchName, accountType, isDefault, isActive } = accountData;

    if (!paymentRequestId) {
        return NextResponse.json({ error: 'paymentRequestId is required' }, { status: 400 });
    }

    // Upsert the payment account
    const paymentAccount = await prisma.paymentAccount.upsert({
      where: {
        bankCode_accountNumber: {
          bankCode,
          accountNumber
        }
      },
      update: {
        accountName,
        bankName,
        branchName,
        accountType,
        isDefault,
        isActive
      },
      create: {
        accountName,
        accountNumber,
        bankCode,
        bankName,
        branchName,
        accountType,
        isDefault,
        isActive
      }
    });

    // Link to the payment request
    await prisma.paymentRequestAccount.create({
      data: {
        paymentRequestId: paymentRequestId,
        paymentAccountId: paymentAccount.id
      }
    });

    return NextResponse.json({ success: true, data: paymentAccount }, { status: 201 });

  } catch (error: any) {
    console.error('Error creating/linking payment account:', error);
    if (error.code === 'P2002') { // Unique constraint failed on junction table
        return NextResponse.json({ error: 'Account already linked to this request' }, { status: 409 });
    }
    return NextResponse.json({ error: 'Failed to save payment account' }, { status: 500 });
  }
}
