"use client";

import { toast } from "sonner";

type SheetDisplayData = {
  headers: (string | number | null)[]; // Allow headers to be string, number, or null
  rows: (string | number | null)[][];
};

// Convert data to CSV format
export const convertToCSV = (
  headers: (string | number | null)[], 
  rows: (string | number | null)[][]
): string => {
  const escapeCSV = (field: any): string => {
    if (field === null || typeof field === 'undefined') {
      return '';
    }
    const stringField = String(field);
    // Check if field contains comma, newline, or double quote
    if (stringField.includes(',') || stringField.includes('\n') || stringField.includes('"')) {
      // Escape double quotes by replacing them with two double quotes, then wrap in double quotes
      return `"${stringField.replace(/"/g, '""')}"`;
    }
    return stringField;
  };

  const headerRow = headers.map(escapeCSV).join(',');
  const dataRows = rows.map(row => row.map(escapeCSV).join(','));
  
  return [headerRow, ...dataRows].join('\n');
};

// Handle CSV download
export const useCSVDownload = (
  sheetData: SheetDisplayData | null,
): void => {
  if (!sheetData || !sheetData.headers || !sheetData.rows || sheetData.rows.length === 0) {
    toast.error("沒有可供下載的資料。");
    return;
  }

  try {
    const csvString = convertToCSV(sheetData.headers, sheetData.rows);
    const blob = new Blob(['\uFEFF' + csvString], { type: 'text/csv;charset=utf-8;' }); // Prepend BOM for Excel
    const link = document.createElement("a");
    const url = URL.createObjectURL(blob);
    link.setAttribute("href", url);
    link.setAttribute("download", `僑星福華社區_${new Date().getFullYear() - 1911}年${new Date().getMonth() + 1}月${new Date().getDate()}日_管理費繳費統計表.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
    toast.success("CSV下載成功！");
  } catch (error) {
    console.error("Error generating CSV: ", error);
    toast.error("CSV下載失敗。");
  }
};