"use client";
import pdfMake from "pdfmake/build/pdfmake";
import { toast } from "sonner";

type SheetDisplayData = {
  headers: (string | number | null)[]; // Allow headers to be string, number, or null
  rows: (string | number | null)[][];
};

export const usePDFDownload = async (
  sheetData: SheetDisplayData | null,
  isFontLoaded: boolean
): Promise<void> => {
  if (
    !sheetData ||
    !sheetData.headers ||
    !sheetData.rows ||
    sheetData.rows.length === 0
  ) {
    toast.error("沒有可供下載的資料製作PDF。");
    return;
  }
  if (!isFontLoaded) {
    toast.error("Font is not loaded yet. Please try again in a moment.");
    return;
  }

  if (
    !sheetData ||
    !sheetData.headers ||
    !sheetData.rows ||
    sheetData.rows.length === 0
  ) {
    toast.error("沒有可供下載的資料製作PDF。");
    return;
  }
  if (!isFontLoaded) {
    toast.error("Font is not loaded yet. Please try again in a moment.");
    return;
  }

  try {
    // Use default pdfMake fonts and let the system handle Chinese characters
    // This avoids font loading issues while still allowing Chinese text to render
    // The PDF will use the system's default font for Chinese characters
    // VFS is now properly initialized at the top of the file

    // Debug: Log the data structure
    //console.log('Headers:', sheetData.headers);
    //console.log('First few rows:', sheetData.rows.slice(0, 3));

    const tableHeaders = sheetData.headers.map((header, index) => {
      const headerText =
        header === null || header === undefined
          ? `Col${index + 1}`
          : String(header);
      return {
        text: headerText,
        style: "tableHeader",
      };
    });

    const tableRows = sheetData.rows
      .map((row, rowIndex) => {
        if (!Array.isArray(row)) {
          console.warn(`Row ${rowIndex} is not an array:`, row);
          return [];
        }

        // Ensure row has same length as headers
        const normalizedRow = [];
        for (let i = 0; i < sheetData.headers.length; i++) {
          const cell = row[i];
          const cellText =
            cell === null || cell === undefined ? "" : String(cell);
          normalizedRow.push({
            text: cellText,
          });
        }
        return normalizedRow;
      })
      .filter((row) => row.length > 0); // Remove empty rows

    // Validate table data before creating PDF
    console.log("Table headers length:", tableHeaders.length);
    console.log("Table rows count:", tableRows.length);
    console.log("First table row length:", tableRows[0]?.length);

    // Ensure all rows have the same length as headers
    const validatedRows = tableRows.map((row, index) => {
      if (row.length !== tableHeaders.length) {
        console.warn(
          `Row ${index} length (${row.length}) doesn't match headers length (${tableHeaders.length})`
        );
        // Pad or trim row to match headers length
        const paddedRow = [...row];
        while (paddedRow.length < tableHeaders.length) {
          paddedRow.push({ text: "" });
        }
        return paddedRow.slice(0, tableHeaders.length);
      }
      return row;
    });

    const columnWidths = tableHeaders.map((header, index) => {
      const headerText = String(header ?? "");

      // Narrow columns for 年/月
      if (/^\d{4}\/\d{2}$/.test(headerText)) {
        return 30; // fixed pixel width (e.g. 35px)
      }

      // 序號, 住戶編號 can also be smaller
      if (["序號"].includes(headerText)) {
        return 24;
      }
      if (["住戶編號"].includes(headerText)) {
        return 36;
      }

      // Keep other columns flexible
      return "*";
    });

    const docDefinition: any = {
      pageOrientation: "landscape",
      pageMargins: [0, 0, 0, 0],
      pageSize: "A4",
      content: [
        {
          text: `僑星福華社區管理費繳費統計表(${new Date().getFullYear() - 1911}年${new Date().getMonth() + 1}月${new Date().getDate()}日)`,
          style: "header",
        },
        {
          stack: [
            {
              table: {
                headerRows: 1,
                widths: columnWidths,
                //widths: Array(tableHeaders.length).fill("*"),
                body: [tableHeaders, ...validatedRows],
              },
              layout: "lightHorizontalLines",
              /*layout: {
                  paddingLeft: function () { return 2; },   // horizontal
                  paddingRight: function () { return 2; },
                  paddingTop: function () { return 1; },    // ↓ reduce top padding
                  paddingBottom: function () { return 1; }  // ↓ reduce bottom padding
                },*/
              style: "tableCell",
            },
          ],
          pageBreak: "avoid", // prevent splitting
        },
      ],
      styles: {
        header: {
          fontSize: 10,
          bold: true,
          alignment: "center",
          margin: [0, 8, 0, 0],
        },
        tableHeader: {
          bold: true,
          fontSize: 7,
          alignment: "right",
          valign: "bottom",
          margin: [0, 4, 0, 0],
          // fillColor: '#eeeeee' // Optional: background color for header
        },
        tableCell: {
          alignment: "right",
          fontSize: 7,
          Padding: 0,
        },
      },
      defaultStyle: {
        font: "微軟正黑體",
      },
    };

    pdfMake
      .createPdf(docDefinition)
      .download(
        `僑星福華社區_${new Date().getFullYear() - 1911}年${new Date().getMonth() + 1}月${new Date().getDate()}日_管理費繳費統計表.pdf`
      );
    toast.success("PDF產生成功並開始下載。");
  } catch (error: any) {
    console.error("Error generating PDF: ", error);
    toast.error(`PDF產生失敗: ${error.message || "Unknown error"}`);
  }
};

export const usePaginatedPDFDownload = async (
  sheetData: SheetDisplayData | null,
  isFontLoaded: boolean,
  pageSize: "A4" | "A3" = "A4"
): Promise<void> => {
  if (
    !sheetData ||
    !sheetData.headers ||
    !sheetData.rows ||
    sheetData.rows.length === 0
  ) {
    toast.error("沒有可供下載的資料製作PDF。");
    return;
  }
  if (!isFontLoaded) {
    toast.error("Font is not loaded yet. Please try again in a moment.");
    return;
  }

  // Page settings for A4 and A3
  const pageConfig = {
    A4: {
      firstPageItems: 43,
      fontSize: 8,
      tableHeaderFontSize: 7,
      pageHeaderFontSize: 10,
      tableHeaderMargin: [0, 4, 0, 0],
      pageMargins: [10, 0, 10, 0],
      tableCellFirstMargin: [2, 0, 0, 0],
      tableCellMargin: [0, 0, 0, 0],
      tableCellLastMargin: [0, 0, 2, 0],
    },
    A3: {
      firstPageItems: 65, // More items for A3
      fontSize: 12, // Larger font for A3
      tableHeaderFontSize: 12,
      pageHeaderFontSize: 14,
      tableHeaderMargin: [0, 8, 0, 0],
      pageMargins: [10, 0, 10, 0],
      tableCellFirstMargin: [6, 0, 0, 0], // Increased for A3
      tableCellMargin: [4, 0, 4, 0], // Increased for A3
      tableCellLastMargin: [0, 0, 6, 0], // Increased for A3
    },
  };

  const {
    firstPageItems,
    fontSize,
    tableHeaderFontSize,
    pageHeaderFontSize,
    tableHeaderMargin,
    pageMargins,
    tableCellFirstMargin,
    tableCellMargin,
    tableCellLastMargin,
  } = pageConfig[pageSize];

  // helper: build a fresh header row each time
  const makeHeaderRow = () =>
    sheetData.headers.map((header, i) => ({
      text: header == null ? `Col${i + 1}` : String(header),
      style: "tableHeader",
    }));

  try {
    const tableHeaders = sheetData.headers.map((header, index) => {
      const headerText =
        header === null || header === undefined
          ? `Col${index + 1}`
          : String(header);
      return {
        text: headerText,
        style: "tableHeader",
      };
    });

    const allRowsData = sheetData.rows;
    const summaryRowData = allRowsData[allRowsData.length - 1];
    const contentRowsData = allRowsData.slice(0, allRowsData.length - 1); // use allRowData if need summary row

    const processRow = (row: (string | number | null)[]) => {
      if (!Array.isArray(row)) {
        console.warn(`Row is not an array:`, row);
        return [];
      }
      const normalizedRow = [];
      const lastColumnIndex = sheetData.headers.length - 1;
      for (let i = 0; i < sheetData.headers.length; i++) {
        const cell = row[i];
        const cellText = cell === null || cell === undefined ? "" : String(cell);

        let cellStyle = "tableCell";
        if (i === 0) {
          cellStyle = "tableCellFirst";
        } else if (i === lastColumnIndex) {
          cellStyle = "tableCellLast";
        }

        normalizedRow.push({
          text: cellText,
          style: cellStyle,
        });
      }
      return normalizedRow;
    };

    const processedRows = contentRowsData
      .map(processRow)
      .filter((row) => row.length > 0);
    const summaryRow = processRow(summaryRowData);

    // Split data into two parts: first page items and the rest
    const firstPageItemCount = firstPageItems - 1; // Make space for summary row
    const firstPageRows = processedRows.slice(0, firstPageItemCount);
    const secondPageRows = processedRows.slice(firstPageItemCount);

    console.log("Table headers length:", tableHeaders.length);
    console.log("First page rows count:", firstPageRows.length);
    console.log("Second page rows count:", secondPageRows.length);

    // Ensure all rows have the same length as headers
    const validateRows = (rows: any[]) => {
      return rows.map((row, index) => {
        if (row.length !== tableHeaders.length) {
          console.warn(
            `Row ${index} length (${row.length}) doesn't match headers length (${tableHeaders.length})`
          );
          // Pad or trim row to match headers length
          const paddedRow = [...row];
          while (paddedRow.length < tableHeaders.length) {
            paddedRow.push({ text: "" });
          }
          return paddedRow.slice(0, tableHeaders.length);
        }
        return row;
      });
    };

    const validatedFirstPageRows = validateRows(firstPageRows);
    const validatedSecondPageRows = validateRows(secondPageRows);
    const validatedSummaryRow = validateRows([summaryRow])[0];

    const columnWidths =
      pageSize === "A3"
        ? tableHeaders.map((header) => {
            const headerText = header.text;

            if (/^\d{4}\/\d{2}$/.test(headerText)) {
              return 34;
            }
            if (["序號"].includes(headerText)) {
              return 26;
            }
            if (["住戶編號"].includes(headerText)) {
              return 40;
            }
            // Make other specific columns narrower if needed
            if (
              [
                "住戶編號",
                "機踏車位",
                "補繳去年",
                "每戶累計",
                "到上個月應繳金額",
                "(欠)預繳金額",
                "(欠)預繳月數", // Assuming this is the last column
              ].includes(headerText)
            )
              return "auto";
            return "*"; // Let the rest be flexible
          })
        : tableHeaders.map((header) => {
            const headerText = header.text;

            if (/^\d{4}\/\d{2}$/.test(headerText)) {
              return 21;
            }
            if (["序號"].includes(headerText)) {
              return 12;
            }
            if (["住戶編號"].includes(headerText)) {
              return 30;
            }
            // Make other specific columns narrower if needed
            if (
              [
                "住戶編號",
                "機踏車位",
                "補繳去年",
                "每戶累計",
                "到上個月應繳金額",
                "(欠)預繳金額",
                "(欠)預繳月數", // Assuming this is the last column
              ].includes(headerText)
            )
              return "auto";
            return "*"; // Let the rest be flexible
          });

    // Create content array with two tables
    const content: any[] = [
      {
        text: `僑星福華社區管理費繳費統計表(${new Date().getFullYear() - 1911}年${new Date().getMonth() + 1}月${new Date().getDate()}日) - B棟`,
        style: "header",
      },
      {
        table: {
          headerRows: 1,
          widths: columnWidths,
          body: [tableHeaders, ...validatedFirstPageRows], // add validatedSummaryRow if needed
        },
        layout: "lightHorizontalLines",
        style: "tableCell",
        pageBreak: validatedSecondPageRows.length > 0 ? "after" : undefined, // Add page break only if there's a second page
      },
    ];

    // Add second page if there are remaining rows
    if (validatedSecondPageRows.length > 0) {
      const headerRow = makeHeaderRow();
      content.push(
        {
          text: `僑星福華社區管理費繳費統計表(${new Date().getFullYear() - 1911}年${new Date().getMonth() + 1}月${new Date().getDate()}日) - A棟`,
          style: "header",
        },
        {
          table: {
            headerRows: 1,
            widths: columnWidths,
            body: [headerRow, ...validatedSecondPageRows],
          },
          layout: "lightHorizontalLines",
          style: "tableCell",
          pageBreak: "auto",
        }
      );
    }

    const docDefinition: any = {
      pageOrientation: "landscape",
      pageMargins: pageMargins,
      pageSize: pageSize,
      content: content,
      styles: {
        header: {
          fontSize: pageHeaderFontSize,
          bold: true,
          alignment: "center",
          margin: [0, 8, 0, 4],
        },
        tableHeader: {
          bold: true,
          fontSize: tableHeaderFontSize,
          alignment: "right",
          valign: "bottom",
          margin: tableHeaderMargin, // [left, top, right, bottom]
          fillColor: "#eeeeee", // Optional: background color for header
        },
        // Style for the FIRST column data cells
        tableCellFirst: {
          alignment: "right", // First column (序號) usually looks better left-aligned
          fontSize: fontSize,
          margin: tableCellFirstMargin,
        },
        tableCell: {
          alignment: "right",
          bold: true,
          fontSize: fontSize,
          Padding: 0,
          margin: tableCellMargin,
          noWrap: true,
        },
        // Style for the LAST column data cells
        tableCellLast: {
          alignment: "right",
          fontSize: fontSize,
          margin: tableCellLastMargin,
        },
      },
      defaultStyle: {
        font: "微軟正黑體",
      },
    };

    pdfMake
      .createPdf(docDefinition)
      .download(
        `僑星福華社區_${new Date().getFullYear() - 1911}年${new Date().getMonth() + 1}月${new Date().getDate()}日_管理費繳費統計表(${pageSize}).pdf`
      );
    toast.success("PDF產生成功並開始下載。");
  } catch (error: any) {
    console.error("Error generating PDF: ", error);
    toast.error(`PDF產生失敗: ${error.message || "Unknown error"}`);
  }
};
