"use client"

import { Trash2, Loader2 } from 'lucide-react';
import { useDeletePaymentRequest } from '@/hooks/use-payment-requests';
import { toast } from 'sonner'

interface DeleteConfirmationModalProps {
  isOpen: boolean;
  paymentRequest: any | null;
  onClose: () => void;
  onConfirm?: () => void; // Optional callback for success
}

export const DeleteConfirmationModal = ({ 
  isOpen, 
  paymentRequest, 
  onClose, 
  onConfirm 
}: DeleteConfirmationModalProps) => {
  const deletePaymentRequestMutation = useDeletePaymentRequest();
  
  if (!isOpen || !paymentRequest) return null;

  const paymentCount = paymentRequest?.payments?.length || 0;
  const isDeleting = deletePaymentRequestMutation.isPending;

  const confirmDelete = async () => {
    try {
      const result = await deletePaymentRequestMutation.mutateAsync(paymentRequest.id);
      if (result.success) {
        toast.success('請款單刪除成功');
        onConfirm?.(); // Call the success callback
        onClose(); // Close the modal
      } else {
        toast.error(result.error || '刪除失敗');
      }
    } catch (error) {
      toast.error('刪除失敗，請重試');
      console.error('Delete error:', error);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white p-6 rounded-lg max-w-md w-full mx-4">
        <h3 className="text-lg font-semibold mb-4 text-red-600">確認刪除</h3>
        <div className="mb-4">
          <p className="mb-2">確定要刪除以下請款單嗎？</p>
          <div className="bg-gray-100 p-3 rounded">
            <p><strong>名稱:</strong> {paymentRequest?.name}</p>
            <p><strong>年月:</strong> {paymentRequest?.year}/{paymentRequest?.month}</p>
            {paymentCount > 0 && (
              <p className="text-red-600"><strong>包含 {paymentCount} 筆付款記錄</strong></p>
            )}
          </div>
          <p className="mt-2 text-red-600 text-sm">⚠️ 此操作無法復原</p>
        </div>
        <div className="flex gap-3 justify-end">
          <button
            onClick={onClose}
            disabled={isDeleting}
            className="px-4 py-2 border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50"
          >
            取消
          </button>
          <button
            onClick={confirmDelete}
            disabled={isDeleting}
            className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 disabled:opacity-50 flex items-center gap-2"
          >
            {isDeleting ? (
              <>
                <Loader2 className="animate-spin" size={16} />
                刪除中...
              </>
            ) : (
              <>
                <Trash2 size={16} />
                確認刪除
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};