"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { PencilIcon } from "lucide-react";
import { useRouter } from "next/navigation";

interface EditReportButtonProps {
  workspaceId: string;
  slug: string;
}

export function EditReportButton({ workspaceId, slug }: EditReportButtonProps) {
  const router = useRouter();

  return (
    <Button
      onClick={() => router.push(`/workspaces/${workspaceId}/reports/${slug}/edit`)}
      variant="outline"
      size="sm"
      className="font-bold gap-2"
    >
      <PencilIcon className="h-4 w-4" />
      Edit Report
    </Button>
  );
}
