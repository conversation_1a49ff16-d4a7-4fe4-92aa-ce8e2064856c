"use client"

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { 
  createPaymentRequest,
  deletePaymentRequest, 
  createPayment, 
  updatePayment, 
  deletePayment,
  updatePaymentShowAccountInfo,
  createBankAccount,
  updateBankAccount,
  deleteBankAccount
} from '@/actions/payments/payment-requests'
import { PaymentAccountType } from "@/lib/types";

export { PaymentAccountType } from '@/lib/types'

export interface PaymentAccount {
  id: string
  accountType: PaymentAccountType
  bankCode: string
  bankName: string
  branchName: string
  accountNumber: string
  accountName: string
  isDefault: boolean
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

export interface PaymentRequest {
  id: string
  name: string
  year: string
  month: string
  paymentAccounts: PaymentAccount[]
  payments: Payment[]
  createdAt: Date
  updatedAt: Date
}

export interface Payment {
  id: string
  sequenceId: string
  accountingSubject: string
  payee: string
  remarks?: string | null
  month: string
  amount: number
  paymentMethod: string
  showAccountInfo: boolean

  remitterAccountId?: string | null
  payeeAccountId?: string | null
  remitterAccount?: PaymentAccount | null
  payeeAccount?: PaymentAccount | null

  paymentRequestId?: string | null
  paymentRequest?: PaymentRequest | null
  createdAt: Date
  updatedAt: Date
}

// Fetch payment requests
export function usePaymentRequests() {
  return useQuery({
    queryKey: ['paymentRequests'],
    queryFn: async (): Promise<PaymentRequest[]> => {
      const response = await fetch('/api/payment-requests')
      if (!response.ok) {
        throw new Error('Failed to fetch payment requests')
      }
      return response.json()
    },
  })
}

// Create payment request mutation
export function useCreatePaymentRequest() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: createPaymentRequest,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['paymentRequests'] })
    },
  })
}

export function useDeletePaymentRequest() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: deletePaymentRequest,
    onSuccess: () => {
      // Invalidate and refetch payment requests
      queryClient.invalidateQueries({ queryKey: ['paymentRequests'] })
    },
    onError: (error) => {
      console.error('Delete payment request error:', error)
    }
  })
}


// Bank account mutations
export function useCreateBankAccount() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: createBankAccount,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['paymentRequests'] })
    },
  })
}

export function useUpdateBankAccount() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) => updateBankAccount(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['paymentRequests'] })
    },
  })
}

export function useDeleteBankAccount() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: deleteBankAccount,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['paymentRequests'] })
    },
  })
}

// Payment mutations
export function useCreatePayment() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: createPayment,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['paymentRequests'] })
    },
  })
}

export function useUpdatePayment() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) => updatePayment(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['paymentRequests'] })
    },
  })
}

export function useDeletePayment() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: deletePayment,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['paymentRequests'] })
    },
  })
}

// Toggle account info mutation
export function useToggleAccountInfo() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: async ({ paymentId, show }: { paymentId: string; show: boolean }) => {
      // Only send the field that is being updated
      const result = await updatePayment(paymentId, { 
        showAccountInfo: show,
      })
      return result
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['paymentRequests'] })
    },
  })
}

// Toggle all account info mutation
export function useToggleAccountInfoAll() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({ paymentRequestId, show }: { paymentRequestId: string; show: boolean }) => {
      const result = await updatePaymentShowAccountInfo(paymentRequestId, { 
        showAccountInfo: show,
      })
      return result
    },
    
    onError: (error) => {
      console.error('Toggle account info error:', error)
    },
    onSuccess: () => {
      // Refresh cached queries so UI updates
      queryClient.invalidateQueries({ queryKey: ['paymentRequests'] })
    },
  })
}
